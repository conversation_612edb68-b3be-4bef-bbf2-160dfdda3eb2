2025-08-02 13:44:59,953 - __main__ - INFO - 启动证书生成工具...
2025-08-02 13:45:00,684 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 13:45:00,984 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 13:45:01,020 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 13:45:01,199 - __main__ - ERROR - 应用程序启动失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0'
2025-08-02 13:45:14,493 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 13:45:32,861 - __main__ - INFO - 启动证书生成工具...
2025-08-02 13:45:33,631 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 13:45:33,952 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 13:45:33,987 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 13:45:33,987 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 13:45:34,172 - __main__ - ERROR - 应用程序启动失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0'
2025-08-02 13:45:37,003 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 13:45:55,951 - __main__ - INFO - 启动证书生成工具...
2025-08-02 13:45:56,681 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 13:45:56,998 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 13:45:57,033 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 13:45:57,033 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 13:45:57,201 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0' (可选功能)
2025-08-02 13:45:57,201 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 13:45:57,398 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 13:45:57,398 - __main__ - INFO - 应用程序界面已初始化
2025-08-02 13:45:57,399 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:19:45,166 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:19:45,782 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:19:46,057 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:19:46,092 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:19:46,093 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:19:46,254 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0' (可选功能)
2025-08-02 16:19:46,254 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:19:46,478 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:19:46,884 - gui.app_controller - ERROR - 应用程序控制器初始化失败: 'MainWindow' object has no attribute 'show_config_dialog'
2025-08-02 16:19:46,884 - __main__ - ERROR - 应用程序启动失败: 应用程序控制器初始化失败: 'MainWindow' object has no attribute 'show_config_dialog'
2025-08-02 16:19:51,522 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:20:10,053 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:20:10,740 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:20:11,009 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:20:11,039 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:20:11,040 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:20:11,210 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0' (可选功能)
2025-08-02 16:20:11,210 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:20:11,381 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:20:11,746 - gui.app_controller - ERROR - 应用程序控制器初始化失败: 'MainWindow' object has no attribute 'show_config_dialog'
2025-08-02 16:20:11,746 - __main__ - ERROR - 应用程序启动失败: 应用程序控制器初始化失败: 'MainWindow' object has no attribute 'show_config_dialog'
2025-08-02 16:20:15,898 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:21:14,887 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:21:15,497 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:21:15,797 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:21:15,828 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:21:15,829 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:21:16,004 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0' (可选功能)
2025-08-02 16:21:16,005 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:21:16,157 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:21:16,541 - gui.app_controller - ERROR - 应用程序控制器初始化失败: 'MainWindow' object has no attribute 'show_config_dialog'
2025-08-02 16:21:16,542 - __main__ - ERROR - 应用程序启动失败: 应用程序控制器初始化失败: 'MainWindow' object has no attribute 'show_config_dialog'
2025-08-02 16:21:28,257 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:22:34,676 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:22:35,389 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:22:35,693 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:22:35,726 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:22:35,726 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:22:35,912 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0' (可选功能)
2025-08-02 16:22:35,913 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:22:36,107 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:22:36,620 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:22:36,621 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:22:36,622 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:22:36,622 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:22:36,622 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:22:36,623 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:22:36,623 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:22:36,624 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:22:36,624 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:22:36,624 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:22:36,625 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:22:36,625 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:22:36,625 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:22:36,626 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:22:36,636 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:22:36,641 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'cry1_evaluation', 'gender', 'mstn', 'seal_image', 'drd4a', 'drd4b', 'f_ker_evaluation', 'pigeon_id', 'ldha', 'signature_image', 'submitter', 'drd4_evaluation', 'sample_date', 'report_number', 'f_ker', 'mstn_evaluation', 'cry1', 'receive_date', 'print_date', 'item_no', 'ldha_evaluation'}
2025-08-02 16:22:36,641 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:22:36,652 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:22:36,656 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'pages', 'report_number', 'signature_image', 'seal_image', 'submitter', 'submit_date'}
2025-08-02 16:22:36,657 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:22:36,662 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:22:36,685 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:22:36,685 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:22:36,688 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:22:36,688 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:22:36,688 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:23:25,444 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:23:25,994 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:23:26,258 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:23:26,295 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:23:26,297 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:23:26,465 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0' (可选功能)
2025-08-02 16:23:26,466 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:23:26,638 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:23:27,122 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:23:27,123 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:23:27,124 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:23:27,124 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:23:27,125 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:23:27,125 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:23:27,125 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:23:27,126 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:23:27,126 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:23:27,126 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:23:27,127 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:23:27,127 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:23:27,127 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:23:27,128 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:23:27,138 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:23:27,142 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'submitter', 'ldha_evaluation', 'signature_image', 'cry1', 'ldha', 'sample_date', 'print_date', 'cry1_evaluation', 'pigeon_id', 'mstn_evaluation', 'gender', 'drd4b', 'report_number', 'mstn', 'drd4_evaluation', 'drd4a', 'f_ker', 'f_ker_evaluation', 'item_no', 'seal_image', 'receive_date'}
2025-08-02 16:23:27,143 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:23:27,154 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:23:27,159 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'submitter', 'pages', 'signature_image', 'seal_image', 'submit_date', 'report_number'}
2025-08-02 16:23:27,160 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:23:27,163 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:23:27,186 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:23:27,187 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:23:27,189 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:23:27,189 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:23:27,190 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:23:36,182 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:23:36,739 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:23:36,997 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:23:37,037 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:23:37,037 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:23:37,188 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0' (可选功能)
2025-08-02 16:23:37,188 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:23:37,354 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:23:37,745 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:23:37,746 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:23:37,747 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:23:37,747 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:23:37,747 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:23:37,748 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:23:37,748 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:23:37,748 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:23:37,749 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:23:37,749 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:23:37,750 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:23:37,750 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:23:37,750 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:23:37,750 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:23:37,761 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:23:37,765 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'seal_image', 'f_ker', 'drd4_evaluation', 'drd4b', 'print_date', 'cry1', 'pigeon_id', 'drd4a', 'report_number', 'mstn', 'sample_date', 'ldha', 'signature_image', 'ldha_evaluation', 'f_ker_evaluation', 'mstn_evaluation', 'item_no', 'submitter', 'receive_date', 'cry1_evaluation', 'gender'}
2025-08-02 16:23:37,765 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:23:37,775 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:23:37,779 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'pages', 'seal_image', 'report_number', 'submitter', 'signature_image', 'submit_date'}
2025-08-02 16:23:37,780 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:23:37,785 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:23:37,810 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:23:37,811 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:23:37,814 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:23:37,814 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:23:37,814 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:26:24,826 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:26:25,534 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:26:25,852 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:26:25,885 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:26:25,885 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:26:26,077 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'gobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0' (可选功能)
2025-08-02 16:26:26,078 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:26:26,266 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:26:26,701 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:26:26,702 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:26:26,703 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:26:26,703 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:26:26,703 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:26:26,704 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:26:26,705 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:26:26,706 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:26:26,707 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:26:26,707 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:26:26,708 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:26:26,708 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:26:26,708 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:26:26,709 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:26:26,718 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:26:26,722 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'report_number', 'mstn', 'drd4b', 'sample_date', 'cry1', 'receive_date', 'ldha_evaluation', 'signature_image', 'print_date', 'seal_image', 'submitter', 'ldha', 'f_ker', 'gender', 'f_ker_evaluation', 'item_no', 'mstn_evaluation', 'pigeon_id', 'cry1_evaluation', 'drd4a', 'drd4_evaluation'}
2025-08-02 16:26:26,722 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:26:26,731 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:26:26,736 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'signature_image', 'report_number', 'seal_image', 'submitter', 'pages', 'submit_date'}
2025-08-02 16:26:26,736 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:26:26,740 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:26:26,763 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:26:26,764 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:26:26,767 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:26:26,767 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:26:26,767 - gui.main_window - INFO - 启动主窗口
2025-08-02 16:26:39,433 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 16:26:39,457 - core.excel_processor - INFO - 列名映射: {}
2025-08-02 16:26:39,457 - core.excel_processor - INFO - 标准化后的列名: ['信鸽飞行能力检测报告单\n和平鸽信鸽竞技服务有限公司', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8']
2025-08-02 16:26:39,458 - core.excel_processor - INFO - 列名映射: {}
2025-08-02 16:26:39,458 - core.excel_processor - INFO - 标准化后的列名: ['信鸽飞行能力检测报告单\n和平鸽信鸽竞技服务有限公司', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8']
2025-08-02 16:26:39,458 - core.excel_processor - WARNING - 数据格式验证失败: ["缺少必需字段: ['item_no', 'pigeon_id']"]
2025-08-02 16:26:39,458 - core.excel_processor - ERROR - 读取文件失败: 数据格式验证失败: 缺少必需字段: ['item_no', 'pigeon_id']
2025-08-02 16:26:42,360 - gui.main_window - ERROR - Excel格式错误: 数据格式验证失败: 缺少必需字段: ['item_no', 'pigeon_id']
2025-08-02 16:26:48,379 - gui.main_window - INFO - 程序正在关闭
2025-08-02 16:26:48,380 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:33:21,460 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:33:23,136 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:33:23,503 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:33:23,537 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:33:23,538 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:33:23,723 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 16:33:23,723 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:33:24,015 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:33:24,459 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:33:24,461 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:33:24,461 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:33:24,461 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:33:24,463 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:33:24,463 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:33:24,463 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:33:24,464 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:33:24,464 - core.main_integration - ERROR - 主程序集成器初始化失败: 需要安装 WeasyPrint 或 ReportLab，请运行: pip install weasyprint 或 pip install reportlab
2025-08-02 16:33:24,464 - gui.app_controller - ERROR - 应用程序控制器初始化失败: 主程序集成器初始化失败: 需要安装 WeasyPrint 或 ReportLab，请运行: pip install weasyprint 或 pip install reportlab
2025-08-02 16:33:24,464 - __main__ - ERROR - 应用程序启动失败: 应用程序控制器初始化失败: 主程序集成器初始化失败: 需要安装 WeasyPrint 或 ReportLab，请运行: pip install weasyprint 或 pip install reportlab
2025-08-02 16:33:30,980 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:33:35,365 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:33:35,857 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:33:36,071 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:33:36,100 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:33:36,100 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:33:36,264 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 16:33:36,264 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:33:36,419 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:33:36,625 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:33:36,625 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:33:36,626 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:33:36,626 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:33:36,626 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:33:36,627 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:33:36,627 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:33:36,627 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:33:36,627 - core.main_integration - ERROR - 主程序集成器初始化失败: 需要安装 WeasyPrint 或 ReportLab，请运行: pip install weasyprint 或 pip install reportlab
2025-08-02 16:33:36,628 - gui.app_controller - ERROR - 应用程序控制器初始化失败: 主程序集成器初始化失败: 需要安装 WeasyPrint 或 ReportLab，请运行: pip install weasyprint 或 pip install reportlab
2025-08-02 16:33:36,628 - __main__ - ERROR - 应用程序启动失败: 应用程序控制器初始化失败: 主程序集成器初始化失败: 需要安装 WeasyPrint 或 ReportLab，请运行: pip install weasyprint 或 pip install reportlab
2025-08-02 16:33:38,301 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:36:23,585 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:36:23,676 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:36:24,119 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:36:24,197 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:36:24,386 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:36:24,422 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:36:24,422 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:36:24,462 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:36:24,494 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:36:24,494 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:36:24,596 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 16:36:24,596 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:36:24,679 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 16:36:24,679 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:36:24,779 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:36:24,854 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:36:25,040 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:36:25,041 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:36:25,042 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:36:25,042 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:36:25,042 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:36:25,042 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:36:25,044 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:36:25,044 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:36:25,044 - core.certificate_generator - WARNING - PDF生成库未安装，PDF生成功能将不可用
2025-08-02 16:36:25,044 - core.certificate_generator - WARNING - PyPDF2未安装，PDF合并功能将不可用
2025-08-02 16:36:25,044 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:36:25,044 - core.report_generator - WARNING - PDF生成库未安装，PDF生成功能将不可用
2025-08-02 16:36:25,044 - core.report_generator - WARNING - PyPDF2未安装，PDF合并功能将不可用
2025-08-02 16:36:25,044 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:36:25,045 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:36:25,045 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:36:25,045 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:36:25,045 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:36:25,054 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:36:25,058 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'signature_image', 'cry1_evaluation', 'f_ker', 'pigeon_id', 'sample_date', 'receive_date', 'ldha_evaluation', 'submitter', 'drd4b', 'drd4_evaluation', 'drd4a', 'f_ker_evaluation', 'cry1', 'report_number', 'mstn', 'print_date', 'seal_image', 'item_no', 'gender', 'mstn_evaluation', 'ldha'}
2025-08-02 16:36:25,058 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:36:25,068 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:36:25,073 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'pages', 'submit_date', 'signature_image', 'seal_image', 'report_number', 'submitter'}
2025-08-02 16:36:25,073 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:36:25,081 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:36:25,105 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:36:25,106 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:36:25,108 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:36:25,108 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:36:25,110 - gui.main_window - INFO - 启动主窗口
2025-08-02 16:36:25,113 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:36:25,114 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:36:25,115 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:36:25,115 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:36:25,116 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:36:25,116 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:36:25,116 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:36:25,117 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:36:25,117 - core.certificate_generator - WARNING - PDF生成库未安装，PDF生成功能将不可用
2025-08-02 16:36:25,117 - core.certificate_generator - WARNING - PyPDF2未安装，PDF合并功能将不可用
2025-08-02 16:36:25,117 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:36:25,117 - core.report_generator - WARNING - PDF生成库未安装，PDF生成功能将不可用
2025-08-02 16:36:25,117 - core.report_generator - WARNING - PyPDF2未安装，PDF合并功能将不可用
2025-08-02 16:36:25,118 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:36:25,118 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:36:25,118 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:36:25,118 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:36:25,119 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:36:25,129 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:36:25,132 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'cry1', 'f_ker', 'gender', 'item_no', 'pigeon_id', 'cry1_evaluation', 'drd4a', 'mstn', 'seal_image', 'ldha_evaluation', 'drd4_evaluation', 'receive_date', 'report_number', 'mstn_evaluation', 'submitter', 'ldha', 'f_ker_evaluation', 'sample_date', 'signature_image', 'print_date', 'drd4b'}
2025-08-02 16:36:25,133 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:36:25,147 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:36:25,153 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'pages', 'seal_image', 'submitter', 'submit_date', 'report_number', 'signature_image'}
2025-08-02 16:36:25,153 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:36:25,159 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:36:25,184 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:36:25,184 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:36:25,187 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:36:25,189 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:36:25,189 - gui.main_window - INFO - 启动主窗口
2025-08-02 16:36:32,527 - gui.main_window - INFO - 程序正在关闭
2025-08-02 16:36:32,528 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:36:38,886 - gui.main_window - INFO - 程序正在关闭
2025-08-02 16:36:38,886 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:36:54,313 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:36:54,842 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:36:55,088 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:36:55,118 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:36:55,119 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:36:55,280 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 16:36:55,280 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:36:55,448 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:36:55,654 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:36:55,655 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:36:55,655 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:36:55,656 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:36:55,656 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:36:55,656 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:36:55,657 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:36:55,657 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:36:55,657 - core.certificate_generator - WARNING - PDF生成库未安装，PDF生成功能将不可用
2025-08-02 16:36:55,657 - core.certificate_generator - WARNING - PyPDF2未安装，PDF合并功能将不可用
2025-08-02 16:36:55,657 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:36:55,657 - core.report_generator - WARNING - PDF生成库未安装，PDF生成功能将不可用
2025-08-02 16:36:55,657 - core.report_generator - WARNING - PyPDF2未安装，PDF合并功能将不可用
2025-08-02 16:36:55,658 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:36:55,658 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:36:55,658 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:36:55,658 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:36:55,658 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:36:55,667 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:36:55,671 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'ldha', 'print_date', 'gender', 'receive_date', 'signature_image', 'ldha_evaluation', 'drd4b', 'cry1_evaluation', 'drd4a', 'item_no', 'report_number', 'submitter', 'seal_image', 'mstn', 'f_ker', 'mstn_evaluation', 'cry1', 'drd4_evaluation', 'pigeon_id', 'f_ker_evaluation', 'sample_date'}
2025-08-02 16:36:55,672 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:36:55,679 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:36:55,683 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'pages', 'submit_date', 'signature_image', 'report_number', 'submitter', 'seal_image'}
2025-08-02 16:36:55,684 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:36:55,688 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:36:55,705 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:36:55,706 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:36:55,708 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:36:55,708 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:36:55,709 - gui.main_window - INFO - 启动主窗口
2025-08-02 16:37:01,495 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 16:37:01,512 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 16:37:01,514 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 16:37:01,514 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 16:37:01,514 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 16:37:01,515 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:37:01,516 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 16:37:01,516 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:37:01,517 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 16:37:01,518 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 16:37:01,519 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 16:37:01,519 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 16:37:01,521 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 16:37:03,078 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx, 记录数: 13
2025-08-02 16:37:08,087 - core.data_validator - INFO - 开始数据清理...
2025-08-02 16:37:08,095 - core.data_validator - INFO - 数据清理完成，从 13 行清理为 13 行
2025-08-02 16:37:08,096 - core.data_validator - INFO - 必需字段验证完成，发现 0 个错误
2025-08-02 16:37:08,098 - core.data_validator - INFO - 数据完整性验证完成，发现 13 个错误
2025-08-02 16:37:18,698 - gui.main_window - INFO - 数据验证完成: 发现 13 个验证错误
2025-08-02 16:37:18,700 - gui.feedback_system - INFO - 状态更新: 数据验证完成
2025-08-02 16:37:21,801 - core.data_validator - INFO - 开始数据清理...
2025-08-02 16:37:21,805 - core.data_validator - INFO - 数据清理完成，从 13 行清理为 13 行
2025-08-02 16:37:22,242 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 16:37:22,268 - gui.main_window - INFO - 数据清理完成
2025-08-02 16:37:22,269 - gui.feedback_system - INFO - 状态更新: 数据清理完成
2025-08-02 16:37:25,773 - core.data_validator - INFO - 开始数据清理...
2025-08-02 16:37:25,777 - core.data_validator - INFO - 数据清理完成，从 13 行清理为 13 行
2025-08-02 16:37:26,165 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 16:37:26,191 - gui.main_window - INFO - 数据清理完成
2025-08-02 16:37:26,194 - gui.feedback_system - INFO - 状态更新: 数据清理完成
2025-08-02 16:37:32,159 - gui.main_window - INFO - 配置对话框已打开
2025-08-02 16:37:53,541 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 16:37:53,559 - gui.app_controller - ERROR - 后台生成证书失败: 'ProgressDialog' object has no attribute 'update_progress'
2025-08-02 16:37:54,861 - gui.app_controller - ERROR - 生成证书失败: 'ProgressDialog' object has no attribute 'show'
2025-08-02 16:37:55,993 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 16:37:56,004 - gui.app_controller - ERROR - 后台生成报告失败: 'ProgressDialog' object has no attribute 'update_progress'
2025-08-02 16:37:57,119 - gui.app_controller - ERROR - 生成报告失败: 'ProgressDialog' object has no attribute 'show'
2025-08-02 16:38:00,635 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 16:38:00,652 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 16:38:00,652 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 16:38:00,653 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 16:38:00,654 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 16:38:00,654 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:38:00,654 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 16:38:00,654 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:38:00,656 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 16:38:00,656 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 16:38:00,657 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 16:38:00,658 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 16:38:00,660 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 16:38:00,692 - gui.main_window - INFO - 数据刷新完成
2025-08-02 16:38:04,332 - gui.main_window - INFO - 程序正在关闭
2025-08-02 16:38:04,332 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:46:03,498 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:46:03,961 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:46:04,191 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:46:04,220 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:46:04,220 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:46:04,384 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 16:46:04,385 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:46:04,574 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:46:04,975 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:46:04,976 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:46:04,976 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:46:04,977 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:46:04,977 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:46:04,977 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:46:04,977 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:46:04,978 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:46:04,978 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:46:04,978 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:46:04,978 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:46:04,978 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:46:04,979 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:46:04,979 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:46:04,986 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:46:04,989 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'pigeon_id', 'seal_image', 'drd4_evaluation', 'sample_date', 'print_date', 'f_ker_evaluation', 'item_no', 'drd4a', 'signature_image', 'submitter', 'receive_date', 'mstn_evaluation', 'mstn', 'cry1_evaluation', 'f_ker', 'gender', 'ldha_evaluation', 'report_number', 'cry1', 'drd4b', 'ldha'}
2025-08-02 16:46:04,989 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:46:04,998 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:46:05,004 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'submit_date', 'pages', 'report_number', 'signature_image', 'submitter', 'seal_image'}
2025-08-02 16:46:05,004 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:46:05,008 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:46:05,024 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:46:05,024 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:46:05,028 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:46:05,028 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:46:05,028 - gui.main_window - INFO - 启动主窗口
2025-08-02 16:47:21,197 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-06-16 王老师第二批 1-145.xlsx
2025-08-02 16:47:21,236 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 16:47:21,237 - core.excel_processor - INFO - 数据框形状: (146, 9)
2025-08-02 16:47:21,238 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 16:47:21,238 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 16:47:21,239 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:47:21,239 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 16:47:21,240 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:47:21,241 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 16:47:21,242 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 16:47:21,254 - core.excel_processor - WARNING - 跳过第 146 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 16:47:21,255 - core.excel_processor - INFO - 成功读取 145 条信鸽数据记录
2025-08-02 16:47:21,263 - gui.components - INFO - 成功加载 145 行数据到表格
2025-08-02 16:47:22,768 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-06-16 王老师第二批 1-145.xlsx, 记录数: 145
2025-08-02 16:47:45,647 - gui.app_controller - INFO - 成功转换 145 条数据记录
2025-08-02 16:47:45,670 - gui.app_controller - ERROR - 后台生成证书失败: 'ProgressDialog' object has no attribute 'update_progress'
2025-08-02 16:47:47,402 - gui.app_controller - ERROR - 生成证书失败: 'ProgressDialog' object has no attribute 'show'
2025-08-02 16:47:49,149 - gui.app_controller - INFO - 成功转换 145 条数据记录
2025-08-02 16:47:49,163 - gui.app_controller - ERROR - 后台生成报告失败: 'ProgressDialog' object has no attribute 'update_progress'
2025-08-02 16:47:50,890 - gui.app_controller - ERROR - 生成报告失败: 'ProgressDialog' object has no attribute 'show'
2025-08-02 16:47:57,961 - gui.main_window - INFO - 配置对话框已打开
2025-08-02 16:48:17,378 - gui.main_window - INFO - 程序正在关闭
2025-08-02 16:48:17,379 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:49:18,686 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:49:19,224 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:49:19,483 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:49:19,518 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:49:19,518 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:49:19,694 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 16:49:19,694 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:49:19,903 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:49:20,289 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:49:20,290 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:49:20,290 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:49:20,292 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:49:20,292 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:49:20,292 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:49:20,292 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:49:20,292 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:49:20,292 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:49:20,293 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:49:20,293 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:49:20,294 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:49:20,294 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:49:20,294 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:49:20,301 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:49:20,303 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'seal_image', 'ldha_evaluation', 'drd4b', 'drd4_evaluation', 'receive_date', 'signature_image', 'f_ker', 'submitter', 'ldha', 'sample_date', 'pigeon_id', 'item_no', 'mstn_evaluation', 'print_date', 'drd4a', 'gender', 'report_number', 'mstn', 'cry1', 'cry1_evaluation', 'f_ker_evaluation'}
2025-08-02 16:49:20,304 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:49:20,316 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:49:20,321 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'submit_date', 'signature_image', 'seal_image', 'submitter', 'pages', 'report_number'}
2025-08-02 16:49:20,322 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:49:20,331 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:49:20,361 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:49:20,361 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:49:20,365 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:49:20,365 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:49:20,365 - gui.main_window - INFO - 启动主窗口
2025-08-02 16:49:24,446 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 16:49:24,472 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 16:49:24,473 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 16:49:24,473 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 16:49:24,474 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 16:49:24,475 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:49:24,475 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 16:49:24,475 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:49:24,478 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 16:49:24,478 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 16:49:24,481 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 16:49:24,481 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 16:49:24,485 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 16:49:25,726 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx, 记录数: 13
2025-08-02 16:49:26,808 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 16:49:26,852 - gui.app_controller - ERROR - 后台生成证书失败: 'ProgressDialog' object has no attribute 'update_progress'
2025-08-02 16:49:28,744 - gui.app_controller - ERROR - 生成证书失败: 'ProgressDialog' object has no attribute 'show'
2025-08-02 16:49:33,323 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:55:00,798 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:55:03,943 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:55:04,369 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:55:04,460 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:55:04,461 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:55:04,732 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 16:55:04,732 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:55:05,487 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:55:06,104 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:55:06,106 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:55:06,107 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:55:06,108 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:55:06,108 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:55:06,108 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:55:06,109 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:55:06,109 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:55:06,109 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:55:06,110 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:55:06,110 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:55:06,111 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:55:06,111 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:55:06,111 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:55:06,147 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:55:06,152 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'receive_date', 'drd4a', 'report_number', 'pigeon_id', 'drd4_evaluation', 'cry1', 'drd4b', 'mstn', 'signature_image', 'item_no', 'f_ker_evaluation', 'cry1_evaluation', 'submitter', 'gender', 'seal_image', 'ldha', 'ldha_evaluation', 'sample_date', 'mstn_evaluation', 'f_ker', 'print_date'}
2025-08-02 16:55:06,153 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:55:06,188 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:55:06,193 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'submitter', 'seal_image', 'pages', 'signature_image', 'report_number', 'submit_date'}
2025-08-02 16:55:06,194 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:55:06,199 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:55:06,271 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:55:06,272 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:55:06,275 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:55:06,275 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:55:06,276 - gui.main_window - INFO - 启动主窗口
2025-08-02 16:55:11,322 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 16:55:11,368 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 16:55:11,370 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 16:55:11,371 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 16:55:11,372 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 16:55:11,373 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:55:11,373 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 16:55:11,374 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:55:11,376 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 16:55:11,377 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 16:55:11,379 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 16:55:11,380 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 16:55:11,383 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 16:55:13,605 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx, 记录数: 13
2025-08-02 16:55:29,411 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 16:55:29,481 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_165529
2025-08-02 16:55:29,520 - core.main_integration - INFO - 数据验证完成: 有效 0/13
2025-08-02 16:55:29,528 - gui.app_controller - ERROR - 后台生成证书失败: 数据验证失败: ['信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空']
2025-08-02 16:55:54,718 - gui.main_window - INFO - 程序正在关闭
2025-08-02 16:55:54,718 - __main__ - INFO - 证书生成工具已关闭
2025-08-02 16:57:52,115 - __main__ - INFO - 启动证书生成工具...
2025-08-02 16:57:52,776 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 16:57:53,056 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 16:57:53,095 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 16:57:53,096 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 16:57:53,279 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 16:57:53,279 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 16:57:53,458 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 16:57:53,846 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 16:57:53,847 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:57:53,847 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:57:53,848 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:57:53,848 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 16:57:53,848 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 16:57:53,848 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 16:57:53,849 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 16:57:53,849 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 16:57:53,849 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 16:57:53,849 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 16:57:53,849 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 16:57:53,849 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 16:57:53,850 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 16:57:53,858 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 16:57:53,862 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'sample_date', 'print_date', 'drd4b', 'drd4_evaluation', 'mstn', 'f_ker_evaluation', 'item_no', 'ldha', 'mstn_evaluation', 'f_ker', 'receive_date', 'gender', 'signature_image', 'cry1', 'seal_image', 'ldha_evaluation', 'report_number', 'cry1_evaluation', 'pigeon_id', 'submitter', 'drd4a'}
2025-08-02 16:57:53,863 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 16:57:53,871 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 16:57:53,877 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'report_number', 'submitter', 'submit_date', 'signature_image', 'pages', 'seal_image'}
2025-08-02 16:57:53,877 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 16:57:53,882 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 16:57:53,905 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 16:57:53,905 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 16:57:53,908 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 16:57:53,909 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 16:57:53,909 - gui.main_window - INFO - 启动主窗口
2025-08-02 16:58:10,119 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 16:58:10,139 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 16:58:10,140 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 16:58:10,141 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 16:58:10,141 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 16:58:10,142 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:58:10,143 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 16:58:10,143 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 16:58:10,144 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 16:58:10,144 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 16:58:10,146 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 16:58:10,147 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 16:58:10,149 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 16:58:12,602 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx, 记录数: 13
2025-08-02 16:58:51,475 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 16:58:51,541 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_165851
2025-08-02 16:58:51,568 - core.main_integration - INFO - 数据验证完成: 有效 0/13
2025-08-02 16:58:51,569 - gui.app_controller - ERROR - 后台生成证书失败: 数据验证失败: ['信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空']
2025-08-02 16:59:03,256 - gui.app_controller - ERROR - 生成证书失败: 数据验证失败: ['信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空']
2025-08-02 16:59:04,899 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 16:59:04,929 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_报告生成_20250802_165904
2025-08-02 16:59:04,933 - core.main_integration - INFO - 数据验证完成: 有效 0/13
2025-08-02 16:59:04,933 - gui.app_controller - ERROR - 后台生成报告失败: 数据验证失败: ['信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空', '信鸽足环号不能为空', '性别不能为空', 'MSTN基因型不能为空', 'F-KER基因型不能为空', 'LDHA基因型不能为空', 'DRD4A基因型不能为空', 'DRD4B基因型不能为空', 'CRY1基因型不能为空']
2025-08-02 17:03:27,044 - __main__ - INFO - 启动证书生成工具...
2025-08-02 17:03:27,594 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 17:03:27,860 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 17:03:27,900 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 17:03:27,901 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 17:03:28,086 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 17:03:28,087 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 17:03:28,728 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 17:03:29,205 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 17:03:29,208 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:03:29,208 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:03:29,208 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:03:29,208 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 17:03:29,209 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:03:29,209 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:03:29,210 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:03:29,210 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 17:03:29,211 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 17:03:29,211 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 17:03:29,211 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 17:03:29,212 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 17:03:29,212 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 17:03:29,237 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 17:03:29,241 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'receive_date', 'mstn_evaluation', 'ldha_evaluation', 'pigeon_id', 'sample_date', 'gender', 'drd4b', 'cry1', 'f_ker_evaluation', 'report_number', 'drd4a', 'ldha', 'signature_image', 'item_no', 'f_ker', 'mstn', 'cry1_evaluation', 'print_date', 'seal_image', 'drd4_evaluation', 'submitter'}
2025-08-02 17:03:29,242 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 17:03:29,264 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 17:03:29,270 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'seal_image', 'signature_image', 'report_number', 'pages', 'submitter', 'submit_date'}
2025-08-02 17:03:29,271 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 17:03:29,278 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:03:29,347 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:03:29,347 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:03:29,358 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:03:29,359 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 17:03:29,359 - gui.main_window - INFO - 启动主窗口
2025-08-02 17:03:35,399 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 17:03:35,425 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 17:03:35,426 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 17:03:35,426 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 17:03:35,427 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 17:03:35,427 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:03:35,427 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 17:03:35,428 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:03:35,428 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 17:03:35,429 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 17:03:35,430 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 17:03:35,430 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 17:03:35,434 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 17:03:36,761 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx, 记录数: 13
2025-08-02 17:03:45,349 - core.data_validator - INFO - 开始数据清理...
2025-08-02 17:03:45,356 - core.data_validator - INFO - 数据清理完成，从 13 行清理为 13 行
2025-08-02 17:03:45,357 - core.data_validator - INFO - 必需字段验证完成，发现 0 个错误
2025-08-02 17:03:45,362 - core.data_validator - INFO - 数据完整性验证完成，发现 0 个错误
2025-08-02 17:03:46,596 - gui.main_window - INFO - 数据验证完成: 数据验证通过
2025-08-02 17:03:46,599 - gui.feedback_system - INFO - 状态更新: 数据验证完成
2025-08-02 17:03:49,171 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 17:03:49,198 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_170349
2025-08-02 17:03:49,201 - core.main_integration - INFO - 数据验证完成: 有效 0/13
2025-08-02 17:03:49,201 - gui.app_controller - ERROR - 后台生成证书失败: 数据验证失败: ['信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空']
2025-08-02 17:03:56,736 - gui.app_controller - ERROR - 生成证书失败: 数据验证失败: ['信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空']
2025-08-02 17:03:57,930 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 17:03:57,956 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_报告生成_20250802_170357
2025-08-02 17:03:57,960 - core.main_integration - INFO - 数据验证完成: 有效 0/13
2025-08-02 17:03:57,960 - gui.app_controller - ERROR - 后台生成报告失败: 数据验证失败: ['信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空']
2025-08-02 17:03:59,251 - gui.app_controller - ERROR - 生成报告失败: 数据验证失败: ['信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空', '信鸽足环号不能为空']
2025-08-02 17:08:00,825 - __main__ - INFO - 启动证书生成工具...
2025-08-02 17:08:01,390 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 17:08:01,685 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 17:08:01,723 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 17:08:01,724 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 17:08:01,882 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 17:08:01,882 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 17:08:02,054 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 17:08:02,459 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 17:08:02,460 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:08:02,461 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:02,461 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:02,461 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 17:08:02,462 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:08:02,463 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:02,463 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:02,463 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 17:08:02,464 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 17:08:02,464 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 17:08:02,465 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 17:08:02,465 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 17:08:02,466 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 17:08:02,475 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 17:08:02,478 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'cry1_evaluation', 'submitter', 'gender', 'mstn_evaluation', 'ldha_evaluation', 'drd4a', 'print_date', 'mstn', 'signature_image', 'report_number', 'drd4b', 'receive_date', 'ldha', 'cry1', 'sample_date', 'pigeon_id', 'seal_image', 'drd4_evaluation', 'f_ker', 'item_no', 'f_ker_evaluation'}
2025-08-02 17:08:02,479 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 17:08:02,487 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 17:08:02,492 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'seal_image', 'pages', 'signature_image', 'submitter', 'submit_date', 'report_number'}
2025-08-02 17:08:02,492 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 17:08:02,496 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:02,519 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:02,520 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:02,523 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:02,523 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 17:08:02,524 - gui.main_window - INFO - 启动主窗口
2025-08-02 17:08:17,275 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 17:08:17,294 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 17:08:17,295 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 17:08:17,295 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 17:08:17,296 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 17:08:17,296 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:08:17,297 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 17:08:17,297 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:08:17,299 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 17:08:17,299 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 17:08:17,300 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 17:08:17,301 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 17:08:17,304 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 17:08:18,367 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx, 记录数: 13
2025-08-02 17:08:20,497 - gui.app_controller - INFO - DataFrame列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:08:20,498 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 17:08:20,526 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_170820
2025-08-02 17:08:20,530 - core.main_integration - INFO - 数据验证完成: 有效 13/13
2025-08-02 17:08:20,538 - core.certificate_generator - INFO - 开始批量生成证书: 13 个
2025-08-02 17:08:20,539 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,543 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,543 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,545 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,555 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0735
2025-08-02 17:08:20,556 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,561 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,561 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,563 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,569 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390009
2025-08-02 17:08:20,570 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,574 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,575 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,577 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,584 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390007
2025-08-02 17:08:20,585 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,589 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,589 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,592 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,596 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390008
2025-08-02 17:08:20,596 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,601 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,601 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,604 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,608 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390006
2025-08-02 17:08:20,609 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,614 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,614 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,616 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,621 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0747
2025-08-02 17:08:20,621 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,626 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,628 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,630 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,635 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0737
2025-08-02 17:08:20,636 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,642 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,642 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,645 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,651 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0750
2025-08-02 17:08:20,652 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,657 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,657 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,659 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,666 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8235
2025-08-02 17:08:20,667 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,672 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,673 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,675 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,680 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 5038
2025-08-02 17:08:20,680 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,688 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,689 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,691 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,695 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8242
2025-08-02 17:08:20,695 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,702 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,703 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,705 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,709 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0744
2025-08-02 17:08:20,709 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:20,713 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:20,714 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:20,718 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:20,722 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8231
2025-08-02 17:08:20,722 - core.certificate_generator - INFO - 批量证书生成完成: 成功 13/13
2025-08-02 17:08:20,722 - core.main_integration - INFO - 单页证书生成完成: 13 个
2025-08-02 17:08:20,915 - core.certificate_generator - INFO - PDF合并完成: output\2025-07-18_陈伟1-13_证书生成_20250802_170820\信鸽基因检测_单页证书合集.pdf (共 13 页)
2025-08-02 17:08:20,916 - core.main_integration - INFO - 文件保存完成: 1 个文件
2025-08-02 17:08:24,451 - gui.app_controller - INFO - 打开输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_170820
2025-08-02 17:08:24,452 - gui.app_controller - INFO - 操作成功完成: 证书生成完成
2025-08-02 17:08:41,302 - gui.app_controller - INFO - DataFrame列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:08:41,302 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 17:08:41,328 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_报告生成_20250802_170841
2025-08-02 17:08:41,333 - core.main_integration - INFO - 数据验证完成: 有效 13/13
2025-08-02 17:08:41,337 - core.report_generator - INFO - 数据分页完成: 13 条记录分为 1 页
2025-08-02 17:08:41,337 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:08:41,342 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:08:41,342 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:08:41,345 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:08:41,354 - core.report_generator - INFO - 聚合报告生成成功: 13 条记录
2025-08-02 17:08:41,355 - core.main_integration - INFO - 聚合报告生成完成
2025-08-02 17:08:41,358 - core.file_manager - INFO - 文件保存成功: output\2025-07-18_陈伟1-13_报告生成_20250802_170841\信鸽基因检测_聚合报告.pdf (3761 字节)
2025-08-02 17:08:41,358 - core.main_integration - INFO - 文件保存完成: 1 个文件
2025-08-02 17:08:42,655 - gui.app_controller - INFO - 打开输出目录: output\2025-07-18_陈伟1-13_报告生成_20250802_170841
2025-08-02 17:08:42,656 - gui.app_controller - INFO - 操作成功完成: 报告生成完成
2025-08-02 17:16:45,398 - __main__ - INFO - 启动证书生成工具...
2025-08-02 17:16:45,955 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 17:16:46,249 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 17:16:46,287 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 17:16:46,287 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 17:16:46,449 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 17:16:46,450 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 17:16:46,609 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 17:16:46,855 - core.font_manager - INFO - 发现中文字体: SimSun -> C:\Windows\Fonts\simsun.ttc
2025-08-02 17:16:46,856 - core.font_manager - INFO - 发现中文字体: SimHei -> C:\Windows\Fonts\simhei.ttf
2025-08-02 17:16:46,856 - core.font_manager - INFO - 发现中文字体: Microsoft YaHei -> C:\Windows\Fonts\msyh.ttc
2025-08-02 17:16:46,857 - core.font_manager - INFO - 发现中文字体: FangSong -> C:\Windows\Fonts\simfang.ttf
2025-08-02 17:16:46,857 - core.font_manager - INFO - 发现中文字体: SimSun -> C:\WINDOWS\Fonts\simsun.ttc
2025-08-02 17:16:46,857 - core.font_manager - INFO - 发现中文字体: SimHei -> C:\WINDOWS\Fonts\simhei.ttf
2025-08-02 17:16:46,858 - core.font_manager - INFO - 发现中文字体: Microsoft YaHei -> C:\WINDOWS\Fonts\msyh.ttc
2025-08-02 17:16:46,858 - core.font_manager - INFO - 发现中文字体: FangSong -> C:\WINDOWS\Fonts\simfang.ttf
2025-08-02 17:16:46,949 - core.font_manager - INFO - 设置默认中文字体: Chinese-Microsoft YaHei
2025-08-02 17:16:46,950 - core.font_manager - INFO - 成功注册中文字体: Microsoft YaHei
2025-08-02 17:16:46,950 - core.font_manager - INFO - 字体管理器初始化完成
2025-08-02 17:16:47,135 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 17:16:47,137 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:16:47,137 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:16:47,138 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:16:47,138 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 17:16:47,138 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:16:47,138 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:16:47,139 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:16:47,139 - core.certificate_generator - INFO - 证书字体配置: {'font_family': "'Microsoft YaHei', 'Microsoft YaHei', 'SimSun', Arial, sans-serif", 'font_size': '12pt', 'line_height': '1.5', 'chinese_font': 'Chinese-Microsoft YaHei', 'chinese_bold_font': 'Chinese-Microsoft YaHei'}
2025-08-02 17:16:47,139 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 17:16:47,139 - core.report_generator - INFO - 报告字体配置: {'font_family': "'Microsoft YaHei', 'Microsoft YaHei', 'SimSun', Arial, sans-serif", 'font_size': '10pt', 'line_height': '1.2', 'header_font_size': '14pt', 'title_font_size': '16pt', 'chinese_font': 'Chinese-Microsoft YaHei', 'chinese_bold_font': 'Chinese-Microsoft YaHei'}
2025-08-02 17:16:47,139 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 17:16:47,140 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 17:16:47,140 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 17:16:47,140 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 17:16:47,140 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 17:16:47,148 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 17:16:47,153 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'print_date', 'mstn_evaluation', 'drd4a', 'signature_image', 'drd4b', 'ldha_evaluation', 'sample_date', 'report_number', 'f_ker_evaluation', 'submitter', 'ldha', 'mstn', 'cry1_evaluation', 'item_no', 'pigeon_id', 'f_ker', 'receive_date', 'gender', 'cry1', 'drd4_evaluation', 'seal_image'}
2025-08-02 17:16:47,153 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 17:16:47,161 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 17:16:47,164 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'report_number', 'signature_image', 'submit_date', 'pages', 'submitter', 'seal_image'}
2025-08-02 17:16:47,165 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 17:16:47,170 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:16:47,193 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:16:47,193 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:16:47,196 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:16:47,196 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 17:16:47,196 - gui.main_window - INFO - 启动主窗口
2025-08-02 17:16:53,165 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 17:16:53,187 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 17:16:53,188 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 17:16:53,190 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 17:16:53,191 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 17:16:53,191 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:16:53,192 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 17:16:53,192 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:16:53,194 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 17:16:53,194 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 17:16:53,196 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 17:16:53,197 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 17:16:53,199 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 17:16:54,478 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx, 记录数: 13
2025-08-02 17:16:56,808 - gui.app_controller - INFO - DataFrame列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:16:56,810 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 17:16:56,885 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_报告生成_20250802_171656
2025-08-02 17:16:56,911 - core.main_integration - INFO - 数据验证完成: 有效 13/13
2025-08-02 17:16:56,922 - core.report_generator - INFO - 数据分页完成: 13 条记录分为 1 页
2025-08-02 17:16:56,922 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:16:56,927 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:16:56,927 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:16:56,929 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:16:56,946 - core.report_generator - INFO - 聚合报告生成成功: 13 条记录
2025-08-02 17:16:56,947 - core.main_integration - INFO - 聚合报告生成完成
2025-08-02 17:16:56,956 - core.file_manager - INFO - 文件保存成功: output\2025-07-18_陈伟1-13_报告生成_20250802_171656\信鸽基因检测_聚合报告.pdf (31092 字节)
2025-08-02 17:16:56,956 - core.main_integration - INFO - 文件保存完成: 1 个文件
2025-08-02 17:16:58,510 - gui.app_controller - INFO - 打开输出目录: output\2025-07-18_陈伟1-13_报告生成_20250802_171656
2025-08-02 17:16:58,511 - gui.app_controller - INFO - 操作成功完成: 报告生成完成
2025-08-02 17:17:18,079 - gui.app_controller - INFO - DataFrame列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:17:18,080 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 17:17:18,105 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_171718
2025-08-02 17:17:18,110 - core.main_integration - INFO - 数据验证完成: 有效 13/13
2025-08-02 17:17:18,143 - core.certificate_generator - INFO - 开始批量生成证书: 13 个
2025-08-02 17:17:18,144 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,151 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,151 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,152 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,167 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0735
2025-08-02 17:17:18,167 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,173 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,173 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,177 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,188 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390009
2025-08-02 17:17:18,188 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,197 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,197 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,200 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,215 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390007
2025-08-02 17:17:18,216 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,221 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,221 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,223 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,237 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390008
2025-08-02 17:17:18,238 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,243 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,245 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,248 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,263 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390006
2025-08-02 17:17:18,264 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,268 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,269 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,271 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,284 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0747
2025-08-02 17:17:18,285 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,290 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,291 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,294 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,305 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0737
2025-08-02 17:17:18,305 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,311 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,312 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,315 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,328 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0750
2025-08-02 17:17:18,328 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,334 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,336 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,341 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,355 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8235
2025-08-02 17:17:18,355 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,360 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,361 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,363 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,378 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 5038
2025-08-02 17:17:18,379 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,386 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,388 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,392 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,408 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8242
2025-08-02 17:17:18,409 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,415 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,418 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,422 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,436 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0744
2025-08-02 17:17:18,437 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:17:18,441 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:17:18,442 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:17:18,444 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:17:18,456 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8231
2025-08-02 17:17:18,456 - core.certificate_generator - INFO - 批量证书生成完成: 成功 13/13
2025-08-02 17:17:18,457 - core.main_integration - INFO - 单页证书生成完成: 13 个
2025-08-02 17:17:18,668 - core.certificate_generator - INFO - PDF合并完成: output\2025-07-18_陈伟1-13_证书生成_20250802_171718\信鸽基因检测_单页证书合集.pdf (共 13 页)
2025-08-02 17:17:18,668 - core.main_integration - INFO - 文件保存完成: 1 个文件
2025-08-02 17:17:19,896 - gui.app_controller - INFO - 打开输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_171718
2025-08-02 17:17:19,897 - gui.app_controller - INFO - 操作成功完成: 证书生成完成
2025-08-02 17:17:43,448 - core.data_validator - INFO - 开始数据清理...
2025-08-02 17:17:43,453 - core.data_validator - INFO - 数据清理完成，从 13 行清理为 13 行
2025-08-02 17:17:43,454 - core.data_validator - INFO - 必需字段验证完成，发现 0 个错误
2025-08-02 17:17:43,456 - core.data_validator - INFO - 数据完整性验证完成，发现 0 个错误
2025-08-02 17:17:44,644 - gui.main_window - INFO - 数据验证完成: 数据验证通过
2025-08-02 17:17:44,645 - gui.feedback_system - INFO - 状态更新: 数据验证完成
2025-08-02 17:17:47,001 - core.data_validator - INFO - 开始数据清理...
2025-08-02 17:17:47,005 - core.data_validator - INFO - 数据清理完成，从 13 行清理为 13 行
2025-08-02 17:17:47,457 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 17:17:47,484 - gui.main_window - INFO - 数据清理完成
2025-08-02 17:17:47,486 - gui.feedback_system - INFO - 状态更新: 数据清理完成
2025-08-02 17:35:17,474 - __main__ - INFO - 启动证书生成工具...
2025-08-02 17:35:18,028 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 17:35:18,359 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 17:35:18,399 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 17:35:18,400 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 17:35:18,559 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 17:35:18,559 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 17:35:18,720 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 17:35:18,950 - core.font_manager - INFO - 发现中文字体: SimSun -> C:\Windows\Fonts\simsun.ttc
2025-08-02 17:35:18,950 - core.font_manager - INFO - 发现中文字体: SimHei -> C:\Windows\Fonts\simhei.ttf
2025-08-02 17:35:18,951 - core.font_manager - INFO - 发现中文字体: Microsoft YaHei -> C:\Windows\Fonts\msyh.ttc
2025-08-02 17:35:18,951 - core.font_manager - INFO - 发现中文字体: FangSong -> C:\Windows\Fonts\simfang.ttf
2025-08-02 17:35:18,952 - core.font_manager - INFO - 发现中文字体: SimSun -> C:\WINDOWS\Fonts\simsun.ttc
2025-08-02 17:35:18,952 - core.font_manager - INFO - 发现中文字体: SimHei -> C:\WINDOWS\Fonts\simhei.ttf
2025-08-02 17:35:18,953 - core.font_manager - INFO - 发现中文字体: Microsoft YaHei -> C:\WINDOWS\Fonts\msyh.ttc
2025-08-02 17:35:18,954 - core.font_manager - INFO - 发现中文字体: FangSong -> C:\WINDOWS\Fonts\simfang.ttf
2025-08-02 17:35:19,044 - core.font_manager - INFO - 设置默认中文字体: Chinese-Microsoft YaHei
2025-08-02 17:35:19,045 - core.font_manager - INFO - 成功注册中文字体: Microsoft YaHei
2025-08-02 17:35:19,045 - core.font_manager - INFO - 字体管理器初始化完成
2025-08-02 17:35:19,203 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 17:35:19,206 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:35:19,206 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:35:19,206 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:35:19,206 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 17:35:19,206 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:35:19,206 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:35:19,207 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:35:19,207 - core.certificate_generator - INFO - 证书字体配置: {'font_family': "'Microsoft YaHei', 'Microsoft YaHei', 'SimSun', Arial, sans-serif", 'font_size': '12pt', 'line_height': '1.5', 'chinese_font': 'Chinese-Microsoft YaHei', 'chinese_bold_font': 'Chinese-Microsoft YaHei'}
2025-08-02 17:35:19,207 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 17:35:19,207 - core.report_generator - INFO - 报告字体配置: {'font_family': "'Microsoft YaHei', 'Microsoft YaHei', 'SimSun', Arial, sans-serif", 'font_size': '10pt', 'line_height': '1.2', 'header_font_size': '14pt', 'title_font_size': '16pt', 'chinese_font': 'Chinese-Microsoft YaHei', 'chinese_bold_font': 'Chinese-Microsoft YaHei'}
2025-08-02 17:35:19,207 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 17:35:19,209 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 17:35:19,209 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 17:35:19,209 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 17:35:19,209 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 17:35:19,217 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 17:35:19,221 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'gender', 'pigeon_id', 'drd4a', 'print_date', 'receive_date', 'seal_image', 'signature_image', 'drd4_evaluation', 'submitter', 'mstn_evaluation', 'ldha', 'mstn', 'f_ker', 'drd4b', 'sample_date', 'ldha_evaluation', 'cry1_evaluation', 'cry1', 'item_no', 'report_number', 'f_ker_evaluation'}
2025-08-02 17:35:19,222 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 17:35:19,230 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 17:35:19,234 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'seal_image', 'submit_date', 'signature_image', 'submitter', 'pages', 'report_number'}
2025-08-02 17:35:19,235 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 17:35:19,240 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:35:19,264 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:35:19,265 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:35:19,267 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:35:19,268 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 17:35:19,268 - gui.main_window - INFO - 启动主窗口
2025-08-02 17:36:45,134 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 17:36:45,160 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 17:36:45,162 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 17:36:45,162 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 17:36:45,163 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 17:36:45,167 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:36:45,167 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 17:36:45,168 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:36:45,169 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 17:36:45,169 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 17:36:45,172 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 17:36:45,172 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 17:36:45,175 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 17:36:46,549 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx, 记录数: 13
2025-08-02 17:36:47,480 - gui.app_controller - INFO - DataFrame列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:36:47,483 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 17:36:47,542 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_173647
2025-08-02 17:36:47,571 - core.main_integration - INFO - 数据验证完成: 有效 13/13
2025-08-02 17:36:47,579 - core.certificate_generator - INFO - 开始批量生成证书: 13 个
2025-08-02 17:36:47,579 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,585 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,586 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,588 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,620 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0735
2025-08-02 17:36:47,620 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,625 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,625 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,628 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,658 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390009
2025-08-02 17:36:47,658 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,663 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,664 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,666 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,697 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390007
2025-08-02 17:36:47,698 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,703 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,704 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,706 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,735 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390008
2025-08-02 17:36:47,736 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,740 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,741 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,744 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,780 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390006
2025-08-02 17:36:47,780 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,784 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,785 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,788 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,817 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0747
2025-08-02 17:36:47,818 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,823 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,824 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,826 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,854 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0737
2025-08-02 17:36:47,855 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,860 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,861 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,864 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,890 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0750
2025-08-02 17:36:47,892 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,896 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,896 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,899 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,927 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8235
2025-08-02 17:36:47,927 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,933 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,933 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,936 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:47,966 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 5038
2025-08-02 17:36:47,966 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:47,971 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:47,971 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:47,973 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:48,000 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8242
2025-08-02 17:36:48,001 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:48,008 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:48,008 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:48,012 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:48,041 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0744
2025-08-02 17:36:48,041 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:36:48,047 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:36:48,047 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:36:48,050 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:36:48,078 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8231
2025-08-02 17:36:48,078 - core.certificate_generator - INFO - 批量证书生成完成: 成功 13/13
2025-08-02 17:36:48,079 - core.main_integration - INFO - 单页证书生成完成: 13 个
2025-08-02 17:36:48,295 - core.certificate_generator - INFO - PDF合并完成: output\2025-07-18_陈伟1-13_证书生成_20250802_173647\信鸽基因检测_单页证书合集.pdf (共 13 页)
2025-08-02 17:36:48,296 - core.main_integration - INFO - 文件保存完成: 1 个文件
2025-08-02 17:36:51,364 - gui.app_controller - INFO - 打开输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_173647
2025-08-02 17:36:51,364 - gui.app_controller - INFO - 操作成功完成: 证书生成完成
2025-08-02 17:37:19,153 - gui.app_controller - INFO - DataFrame列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:37:19,157 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 17:37:19,183 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_报告生成_20250802_173719
2025-08-02 17:37:19,186 - core.main_integration - INFO - 数据验证完成: 有效 13/13
2025-08-02 17:37:19,196 - core.report_generator - INFO - 数据分页完成: 13 条记录分为 1 页
2025-08-02 17:37:19,196 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:37:19,202 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:37:19,202 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:37:19,205 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:37:19,223 - core.report_generator - INFO - 聚合报告生成成功: 13 条记录
2025-08-02 17:37:19,224 - core.main_integration - INFO - 聚合报告生成完成
2025-08-02 17:37:19,229 - core.file_manager - INFO - 文件保存成功: output\2025-07-18_陈伟1-13_报告生成_20250802_173719\信鸽基因检测_聚合报告.pdf (57424 字节)
2025-08-02 17:37:19,230 - core.main_integration - INFO - 文件保存完成: 1 个文件
2025-08-02 17:37:20,853 - gui.app_controller - INFO - 打开输出目录: output\2025-07-18_陈伟1-13_报告生成_20250802_173719
2025-08-02 17:37:20,853 - gui.app_controller - INFO - 操作成功完成: 报告生成完成
2025-08-02 17:44:50,534 - __main__ - INFO - 启动证书生成工具...
2025-08-02 17:44:51,129 - __main__ - INFO - ✓ pandas 已安装
2025-08-02 17:44:51,427 - __main__ - INFO - ✓ openpyxl 已安装
2025-08-02 17:44:51,461 - __main__ - INFO - ✓ jinja2 已安装
2025-08-02 17:44:51,462 - __main__ - INFO - ✓ PIL 已安装
2025-08-02 17:44:51,628 - __main__ - WARNING - ⚠ weasyprint 导入失败: cannot load library 'libgobject-2.0-0': error 0x7e.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libgobject-2.0-0' (可选功能)
2025-08-02 17:44:51,629 - __main__ - WARNING - 以下可选依赖包未安装：weasyprint
PDF生成功能可能无法正常工作。
如需完整功能，请安装：pip install weasyprint
2025-08-02 17:44:51,796 - core.config_manager - INFO - 配置文件加载成功: config\settings.json
2025-08-02 17:44:52,061 - core.font_manager - INFO - 发现中文字体: SimSun -> C:\Windows\Fonts\simsun.ttc
2025-08-02 17:44:52,062 - core.font_manager - INFO - 发现中文字体: SimHei -> C:\Windows\Fonts\simhei.ttf
2025-08-02 17:44:52,062 - core.font_manager - INFO - 发现中文字体: Microsoft YaHei -> C:\Windows\Fonts\msyh.ttc
2025-08-02 17:44:52,063 - core.font_manager - INFO - 发现中文字体: FangSong -> C:\Windows\Fonts\simfang.ttf
2025-08-02 17:44:52,063 - core.font_manager - INFO - 发现中文字体: SimSun -> C:\WINDOWS\Fonts\simsun.ttc
2025-08-02 17:44:52,064 - core.font_manager - INFO - 发现中文字体: SimHei -> C:\WINDOWS\Fonts\simhei.ttf
2025-08-02 17:44:52,064 - core.font_manager - INFO - 发现中文字体: Microsoft YaHei -> C:\WINDOWS\Fonts\msyh.ttc
2025-08-02 17:44:52,065 - core.font_manager - INFO - 发现中文字体: FangSong -> C:\WINDOWS\Fonts\simfang.ttf
2025-08-02 17:44:52,152 - core.font_manager - INFO - 设置默认中文字体: Chinese-Microsoft YaHei
2025-08-02 17:44:52,153 - core.font_manager - INFO - 成功注册中文字体: Microsoft YaHei
2025-08-02 17:44:52,153 - core.font_manager - INFO - 字体管理器初始化完成
2025-08-02 17:44:52,334 - gui.main_window - INFO - 主窗口初始化完成
2025-08-02 17:44:52,336 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:44:52,337 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:44:52,337 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:44:52,337 - core.template_manager - INFO - 模板管理器初始化完成，模板目录: templates
2025-08-02 17:44:52,338 - core.image_processor - INFO - 图像处理器初始化完成，资源目录: assets
2025-08-02 17:44:52,338 - core.image_processor - INFO - 发现资料目录中的印章图片: 资料\印章.png
2025-08-02 17:44:52,338 - core.image_processor - INFO - 发现资料目录中的签字图片: 资料\签字.png
2025-08-02 17:44:52,340 - core.certificate_generator - INFO - 证书字体配置: {'font_family': "'Microsoft YaHei', 'Microsoft YaHei', 'SimSun', Arial, sans-serif", 'font_size': '12pt', 'line_height': '1.5', 'chinese_font': 'Chinese-Microsoft YaHei', 'chinese_bold_font': 'Chinese-Microsoft YaHei'}
2025-08-02 17:44:52,340 - core.certificate_generator - INFO - 证书生成器初始化完成
2025-08-02 17:44:52,340 - core.report_generator - INFO - 报告字体配置: {'font_family': "'Microsoft YaHei', 'Microsoft YaHei', 'SimSun', Arial, sans-serif", 'font_size': '10pt', 'line_height': '1.2', 'header_font_size': '14pt', 'title_font_size': '16pt', 'chinese_font': 'Chinese-Microsoft YaHei', 'chinese_bold_font': 'Chinese-Microsoft YaHei'}
2025-08-02 17:44:52,341 - core.report_generator - INFO - 聚合报告生成器初始化完成
2025-08-02 17:44:52,341 - core.file_manager - INFO - 文件管理器初始化完成，基础输出目录: output
2025-08-02 17:44:52,342 - core.main_integration - INFO - 主程序集成器初始化完成
2025-08-02 17:44:52,342 - gui.app_controller - INFO - 应用程序控制器初始化完成
2025-08-02 17:44:52,342 - __main__ - INFO - 应用程序控制器已初始化
2025-08-02 17:44:52,351 - core.template_manager - INFO - 成功加载模板: certificate_template.html
2025-08-02 17:44:52,356 - core.template_manager - INFO - 模板 certificate_template.html 使用的变量: {'f_ker', 'submitter', 'sample_date', 'drd4b', 'f_ker_evaluation', 'ldha_evaluation', 'mstn', 'receive_date', 'item_no', 'report_number', 'drd4_evaluation', 'drd4a', 'pigeon_id', 'mstn_evaluation', 'cry1', 'gender', 'cry1_evaluation', 'signature_image', 'seal_image', 'ldha', 'print_date'}
2025-08-02 17:44:52,356 - core.template_manager - INFO - 模板验证通过: certificate_template.html
2025-08-02 17:44:52,366 - core.template_manager - INFO - 成功加载模板: report_template.html
2025-08-02 17:44:52,372 - core.template_manager - INFO - 模板 report_template.html 使用的变量: {'pages', 'report_number', 'submitter', 'signature_image', 'seal_image', 'submit_date'}
2025-08-02 17:44:52,374 - core.template_manager - INFO - 模板验证通过: report_template.html
2025-08-02 17:44:52,379 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:44:52,405 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:44:52,405 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:44:52,408 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:44:52,408 - gui.app_controller - INFO - 启动证书生成工具
2025-08-02 17:44:52,408 - gui.main_window - INFO - 启动主窗口
2025-08-02 17:44:57,400 - core.excel_processor - INFO - 读取Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx
2025-08-02 17:44:57,422 - core.excel_processor - INFO - Excel文件读取完成，跳过第1行标题，使用第2行作为列名，读取A-I列
2025-08-02 17:44:57,424 - core.excel_processor - INFO - 数据框形状: (14, 9)
2025-08-02 17:44:57,424 - core.excel_processor - INFO - 列名: ['\n检测序号Item No.\n', '\n信鸽足环号\nPigeons ID No.\n', '\n性别\nMale\n', '乳酸脱氢酶LDHA', '多巴胺D4受体\nDRD4a', '多巴胺D4受体DRD4b', '隐花色素\nCRY1', '肌肉质量\nMSTN', '羽翼指标\nF-KER']
2025-08-02 17:44:57,427 - core.excel_processor - INFO - 列名映射: {'\n检测序号Item No.\n': 'item_no', '\n信鸽足环号\nPigeons ID No.\n': 'pigeon_id', '\n性别\nMale\n': 'gender', '乳酸脱氢酶LDHA': 'ldha', '多巴胺D4受体\nDRD4a': 'drd4a', '多巴胺D4受体DRD4b': 'drd4b', '隐花色素\nCRY1': 'cry1', '肌肉质量\nMSTN': 'mstn', '羽翼指标\nF-KER': 'f_ker'}
2025-08-02 17:44:57,427 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:44:57,427 - core.excel_processor - INFO - 列名映射: {'item_no': 'item_no', 'pigeon_id': 'pigeon_id', 'gender': 'gender', 'ldha': 'ldha', 'drd4a': 'drd4a', 'drd4b': 'drd4b', 'cry1': 'cry1', 'mstn': 'mstn', 'f_ker': 'f_ker'}
2025-08-02 17:44:57,428 - core.excel_processor - INFO - 标准化后的列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:44:57,429 - core.excel_processor - WARNING - 有 1 行信鸽足环号为空，这些行将被跳过
2025-08-02 17:44:57,430 - core.excel_processor - INFO - 数据格式验证通过
2025-08-02 17:44:57,431 - core.excel_processor - WARNING - 跳过第 15 行（关键字段为空）: item_no=LDHA(耐力指标）__判断标准：1. AA型 （极佳） 2. AG型 （优秀）3. GG型 （普通）

DRD4(归巢能力）__判断标准：1. CTCT型 /CTTT型（极佳）TTCT型（极佳） 2. CCTT型（优秀）CCCT型（优秀） CTCC型（优秀）TTCC型（优秀） 3. CCCC型 （普通）

CRY1（导航能力和昼夜节律）__判定标准：1. TT/TT型（极佳） 2. AG/TT型（ 优秀）3. AG/AG型（ 一般）AT/TT型（极佳）

MSTN (肌肉质量） 1. CC(优秀） 2. CT (良好）  3. TT (普通） 

F-KER (羽翼指标）1. T/T (优秀） 2. GT (良好）  3. GG (普通）

GSR (阴雨天定向指标） 1. TT(优秀） 2. CT (良好  3. CC(普通） ）

CASK (智商指标）1. A/A (优秀） 2. G/A (良好）  3. GG (普通）      
鉴定说明：
鉴于国内外多篇研究和实际信鸽育种经验，认为乳酸去氢酶A（LDHA), 多巴胺D4受体（DRD4)是影响比赛成绩的重要遗传基因。

LDHA主要参与乳酸代谢循环，当心个长途飞行时，大量的乳酸会堆积体内，乳酸代谢能力越好的信鸽不容易疲惫，也不易产生肌肉疼痛，耐力和飞行能力也就越好

DRD4基因具备在动物大脑内传递神经递质多巴胺信号能，其与信鸽归巢能力有关，归巢性越强的赛鸽，往往成绩越优异

CRY1 隐花色素基因序列，可能与赛鸽的导航能力和昼夜节律有关，赛格视网膜中隐花色素蛋白，能作为紫外线和地磁的感应器，是帮助信鸽归巢的能力之一
上诉检测数据可作为赛鸽选种和选拔上的参考指标

GSR 与磁感受有关，是信鸽生物罗盘通道的候选基因

CASK 钙/钙调素依赖性丝氨酸蛋白激酶（CASK)是一种参与组织发育和细胞信号传导的多结构蛋白，在骨骼肌重参与神经肌肉的连接发育

, pigeon_id=nan
2025-08-02 17:44:57,431 - core.excel_processor - INFO - 成功读取 13 条信鸽数据记录
2025-08-02 17:44:57,434 - gui.components - INFO - 成功加载 13 行数据到表格
2025-08-02 17:44:58,750 - gui.main_window - INFO - 成功导入Excel文件: C:/Users/<USER>/OneDrive/AutomateShare/30 项目开发/202508 小程序-证书生成器/01 客户需求/2025-07-18 陈伟1-13.xlsx, 记录数: 13
2025-08-02 17:44:59,827 - gui.app_controller - INFO - DataFrame列名: ['item_no', 'pigeon_id', 'gender', 'ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
2025-08-02 17:44:59,829 - gui.app_controller - INFO - 成功转换 13 条数据记录
2025-08-02 17:44:59,877 - core.file_manager - INFO - 创建输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_174459
2025-08-02 17:44:59,902 - core.main_integration - INFO - 数据验证完成: 有效 13/13
2025-08-02 17:44:59,907 - core.certificate_generator - INFO - 开始批量生成证书: 13 个
2025-08-02 17:44:59,908 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:44:59,913 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:44:59,914 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:44:59,917 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:44:59,948 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0735
2025-08-02 17:44:59,948 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:44:59,954 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:44:59,955 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:44:59,958 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:44:59,987 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390009
2025-08-02 17:44:59,988 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:44:59,992 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:44:59,993 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:44:59,994 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,024 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390007
2025-08-02 17:45:00,024 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,029 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,029 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,031 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,062 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390008
2025-08-02 17:45:00,062 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,068 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,069 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,073 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,100 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0390006
2025-08-02 17:45:00,101 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,107 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,107 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,110 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,137 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0747
2025-08-02 17:45:00,138 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,144 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,144 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,146 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,177 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0737
2025-08-02 17:45:00,178 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,183 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,183 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,186 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,214 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0750
2025-08-02 17:45:00,215 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,220 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,220 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,223 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,250 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8235
2025-08-02 17:45:00,250 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,255 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,256 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,259 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,289 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 5038
2025-08-02 17:45:00,290 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,296 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,297 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,300 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,332 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8242
2025-08-02 17:45:00,332 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,337 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,337 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,340 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,375 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 0744
2025-08-02 17:45:00,375 - core.image_processor - INFO - 使用资料目录中的印章图片: 资料\印章.png
2025-08-02 17:45:00,381 - core.image_processor - INFO - 印章图片处理完成: 资料\印章.png
2025-08-02 17:45:00,381 - core.image_processor - INFO - 使用资料目录中的签字图片: 资料\签字.png
2025-08-02 17:45:00,384 - core.image_processor - INFO - 签字图片处理完成: 资料\签字.png
2025-08-02 17:45:00,416 - core.certificate_generator - INFO - 单页证书生成成功: 信鸽ID 8231
2025-08-02 17:45:00,416 - core.certificate_generator - INFO - 批量证书生成完成: 成功 13/13
2025-08-02 17:45:00,416 - core.main_integration - INFO - 单页证书生成完成: 13 个
2025-08-02 17:45:00,697 - core.certificate_generator - INFO - PDF合并完成: output\2025-07-18_陈伟1-13_证书生成_20250802_174459\信鸽基因检测_单页证书合集.pdf (共 13 页)
2025-08-02 17:45:00,698 - core.main_integration - INFO - 文件保存完成: 1 个文件
2025-08-02 17:45:01,810 - gui.app_controller - INFO - 打开输出目录: output\2025-07-18_陈伟1-13_证书生成_20250802_174459
2025-08-02 17:45:01,810 - gui.app_controller - INFO - 操作成功完成: 证书生成完成
