#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证器模块 - 实现数据验证和清理功能
Data Validator Module - Implement data validation and cleaning functionality
"""

import pandas as pd
import re
import logging
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime, date
from .data_models import PigeonData

logger = logging.getLogger(__name__)


class DataValidationError(Exception):
    """数据验证错误异常"""
    pass


class DataValidator:
    """
    数据验证器
    Data Validator
    
    负责验证信鸽基因检测数据的完整性和格式正确性，并提供数据清理功能
    Responsible for validating pigeon genetic testing data integrity and format correctness, and providing data cleaning functionality
    """
    
    # 必需字段定义
    REQUIRED_FIELDS = ['item_no', 'pigeon_id']
    
    # 基因型字段定义
    GENETIC_FIELDS = ['ldha', 'drd4a', 'drd4b', 'cry1', 'mstn', 'f_ker']
    
    # 有效的基因型模式（更宽松的正则表达式）
    VALID_GENOTYPE_PATTERNS = {
        'ldha': r'^[ATCG]{1,2}(/[ATCG]{1,2})?$',
        'drd4a': r'^[ATCG]{1,2}(/[ATCG]{1,2})?$',
        'drd4b': r'^[ATCG]{1,2}(/[ATCG]{1,2})?$',
        'cry1': r'^[ATCG]{1,2}(/[ATCG]{1,2})?$',
        'mstn': r'^[ATCG]{1,2}(/[ATCG]{1,2})?$',
        'f_ker': r'^[ATCG]{1,2}(/[ATCG]{1,2})?$'
    }
    
    # 性别有效值（更宽松的验证）
    VALID_GENDERS = {'MALE', 'FEMALE', 'M', 'F', '雄', '雌', '公', '母', 'N/A', 'NA', 'NAN', None}
    
    def __init__(self):
        """初始化数据验证器"""
        self.validation_errors: List[Dict[str, Any]] = []
        self.cleaned_data: List[PigeonData] = []
        
    def validate_required_fields(self, dataframe: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        验证必需字段
        Validate required fields
        
        Args:
            dataframe: 要验证的数据框
            
        Returns:
            List[Dict[str, Any]]: 验证错误列表，每个错误包含行号、字段名和错误信息
        """
        errors = []
        
        # 检查必需字段是否存在
        missing_columns = []
        for field in self.REQUIRED_FIELDS:
            if field not in dataframe.columns:
                missing_columns.append(field)
        
        if missing_columns:
            errors.append({
                'row': -1,  # -1 表示整体结构错误
                'field': 'columns',
                'error_type': 'missing_columns',
                'message': f'缺少必需列: {missing_columns}',
                'value': None
            })
            return errors
        
        # 逐行检查必需字段的值
        for index, row in dataframe.iterrows():
            for field in self.REQUIRED_FIELDS:
                value = row.get(field)
                
                # 检查空值
                if pd.isna(value) or str(value).strip() == '':
                    errors.append({
                        'row': index + 1,
                        'field': field,
                        'error_type': 'empty_required_field',
                        'message': f'必需字段 {field} 为空',
                        'value': value
                    })
                
                # 检查检测序号格式
                elif field == 'item_no':
                    cleaned_value = self._clean_item_no(str(value))
                    if not self._is_valid_item_no(cleaned_value):
                        errors.append({
                            'row': index + 1,
                            'field': field,
                            'error_type': 'invalid_format',
                            'message': f'检测序号格式无效: {value}',
                            'value': value
                        })
                
                # 检查信鸽足环号格式
                elif field == 'pigeon_id':
                    cleaned_value = str(value).strip()
                    if not self._is_valid_pigeon_id(cleaned_value):
                        errors.append({
                            'row': index + 1,
                            'field': field,
                            'error_type': 'invalid_format',
                            'message': f'信鸽足环号格式无效: {value}',
                            'value': value
                        })
        
        logger.info(f"必需字段验证完成，发现 {len(errors)} 个错误")
        return errors
    
    def validate_data_integrity(self, dataframe: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        验证数据完整性
        Validate data integrity
        
        Args:
            dataframe: 要验证的数据框
            
        Returns:
            List[Dict[str, Any]]: 完整性验证错误列表
        """
        errors = []
        
        # 检查重复的检测序号
        if 'item_no' in dataframe.columns:
            item_nos = dataframe['item_no'].apply(lambda x: self._clean_item_no(str(x)) if pd.notna(x) else '')
            duplicates = item_nos[item_nos.duplicated()].unique()
            
            for duplicate in duplicates:
                if duplicate:  # 排除空值
                    duplicate_rows = dataframe[item_nos == duplicate].index + 1
                    errors.append({
                        'row': list(duplicate_rows),
                        'field': 'item_no',
                        'error_type': 'duplicate_value',
                        'message': f'检测序号重复: {duplicate}',
                        'value': duplicate
                    })
        
        # 检查重复的信鸽足环号
        if 'pigeon_id' in dataframe.columns:
            pigeon_ids = dataframe['pigeon_id'].apply(lambda x: str(x).strip() if pd.notna(x) else '')
            duplicates = pigeon_ids[pigeon_ids.duplicated()].unique()
            
            for duplicate in duplicates:
                if duplicate:  # 排除空值
                    duplicate_rows = dataframe[pigeon_ids == duplicate].index + 1
                    errors.append({
                        'row': list(duplicate_rows),
                        'field': 'pigeon_id',
                        'error_type': 'duplicate_value',
                        'message': f'信鸽足环号重复: {duplicate}',
                        'value': duplicate
                    })
        
        # 检查基因型数据格式
        for field in self.GENETIC_FIELDS:
            if field in dataframe.columns:
                pattern = self.VALID_GENOTYPE_PATTERNS.get(field)
                if pattern:
                    for index, row in dataframe.iterrows():
                        value = row.get(field)
                        if pd.notna(value) and str(value).strip():
                            cleaned_value = self._clean_genotype(str(value))
                            if not re.match(pattern, cleaned_value, re.IGNORECASE):
                                errors.append({
                                    'row': index + 1,
                                    'field': field,
                                    'error_type': 'invalid_genotype',
                                    'message': f'基因型格式无效: {value} (期望格式: {pattern})',
                                    'value': value
                                })
        
        # 检查性别字段
        if 'gender' in dataframe.columns:
            for index, row in dataframe.iterrows():
                value = row.get('gender')
                if pd.notna(value) and str(value).strip():
                    cleaned_value = str(value).strip().upper()
                    if cleaned_value not in self.VALID_GENDERS:
                        errors.append({
                            'row': index + 1,
                            'field': 'gender',
                            'error_type': 'invalid_gender',
                            'message': f'性别值无效: {value} (有效值: {self.VALID_GENDERS})',
                            'value': value
                        })
        
        logger.info(f"数据完整性验证完成，发现 {len(errors)} 个错误")
        return errors
    
    def clean_data(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        清理数据
        Clean data
        
        Args:
            dataframe: 要清理的数据框
            
        Returns:
            pd.DataFrame: 清理后的数据框
        """
        logger.info("开始数据清理...")
        
        # 创建数据框副本
        cleaned_df = dataframe.copy()
        
        # 清理检测序号
        if 'item_no' in cleaned_df.columns:
            cleaned_df['item_no'] = cleaned_df['item_no'].apply(
                lambda x: self._clean_item_no(str(x)) if pd.notna(x) else ''
            )
        
        # 清理信鸽足环号
        if 'pigeon_id' in cleaned_df.columns:
            cleaned_df['pigeon_id'] = cleaned_df['pigeon_id'].apply(
                lambda x: str(x).strip() if pd.notna(x) else ''
            )
        
        # 清理性别字段
        if 'gender' in cleaned_df.columns:
            cleaned_df['gender'] = cleaned_df['gender'].apply(self._clean_gender)
        
        # 清理基因型字段
        for field in self.GENETIC_FIELDS:
            if field in cleaned_df.columns:
                cleaned_df[field] = cleaned_df[field].apply(self._clean_genotype)
        
        # 处理空值
        cleaned_df = self._handle_null_values(cleaned_df)
        
        # 移除完全空白的行
        cleaned_df = self._remove_empty_rows(cleaned_df)
        
        logger.info(f"数据清理完成，从 {len(dataframe)} 行清理为 {len(cleaned_df)} 行")
        return cleaned_df
    
    def validate_and_clean(self, dataframe: pd.DataFrame, strict_mode: bool = False) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """
        验证并清理数据
        Validate and clean data
        
        Args:
            dataframe: 要处理的数据框
            strict_mode: 严格模式，如果为True则在发现错误时抛出异常
            
        Returns:
            Tuple[pd.DataFrame, List[Dict[str, Any]]]: (清理后的数据框, 验证错误列表)
            
        Raises:
            DataValidationError: 严格模式下发现验证错误时抛出
        """
        # 首先清理数据
        cleaned_df = self.clean_data(dataframe)
        
        # 然后验证清理后的数据
        required_errors = self.validate_required_fields(cleaned_df)
        integrity_errors = self.validate_data_integrity(cleaned_df)
        
        all_errors = required_errors + integrity_errors
        self.validation_errors = all_errors
        
        if strict_mode and all_errors:
            error_summary = self._format_error_summary(all_errors)
            raise DataValidationError(f"数据验证失败:\n{error_summary}")
        
        return cleaned_df, all_errors
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """
        获取验证摘要
        Get validation summary
        
        Returns:
            Dict[str, Any]: 验证摘要信息
        """
        if not self.validation_errors:
            return {
                'total_errors': 0,
                'error_types': {},
                'is_valid': True,
                'message': '数据验证通过'
            }
        
        error_types = {}
        for error in self.validation_errors:
            error_type = error['error_type']
            if error_type not in error_types:
                error_types[error_type] = 0
            error_types[error_type] += 1
        
        return {
            'total_errors': len(self.validation_errors),
            'error_types': error_types,
            'is_valid': False,
            'errors': self.validation_errors[:10],  # 只显示前10个错误
            'message': f'发现 {len(self.validation_errors)} 个验证错误'
        }
    
    def _clean_item_no(self, value: str) -> str:
        """清理检测序号"""
        if not value or pd.isna(value):
            return ''
        
        # 转换为字符串并去除空白
        cleaned = str(value).strip()
        
        # 移除各种引号
        quotes_to_remove = [chr(8216), chr(8217), "'", '"']  # ', ', ', "
        while cleaned and cleaned[0] in quotes_to_remove:
            cleaned = cleaned[1:]
        
        # 如果是纯数字且长度小于3，补充前导零
        if cleaned.isdigit() and len(cleaned) < 3:
            cleaned = cleaned.zfill(3)
        
        return cleaned
    
    def _clean_genotype(self, value: Any) -> Optional[str]:
        """清理基因型数据"""
        if pd.isna(value) or str(value).strip() in ['', 'N/A', 'NA', 'NULL']:
            return None
        
        # 转换为字符串并去除空白
        cleaned = str(value).strip().upper()
        
        # 标准化分隔符
        cleaned = cleaned.replace('/', '/').replace('\\', '/')
        
        return cleaned
    
    def _clean_gender(self, value: Any) -> Optional[str]:
        """清理性别字段"""
        if pd.isna(value) or str(value).strip() in ['', 'N/A', 'NA', 'NULL']:
            return None
        
        cleaned = str(value).strip().upper()
        
        # 标准化性别值
        gender_mapping = {
            'MALE': 'MALE',
            'M': 'MALE',
            '雄': 'MALE',
            'FEMALE': 'FEMALE',
            'F': 'FEMALE',
            '雌': 'FEMALE'
        }
        
        return gender_mapping.get(cleaned, cleaned)
    
    def _is_valid_item_no(self, value: str) -> bool:
        """验证检测序号格式"""
        if not value:
            return False
        
        # 检测序号应该是数字或以数字开头的字符串
        return bool(re.match(r'^\d+', value))
    
    def _is_valid_pigeon_id(self, value: str) -> bool:
        """验证信鸽足环号格式"""
        if not value:
            return False
        
        # 信鸽足环号应该包含数字，可以包含字母和特殊字符
        return bool(re.search(r'\d', value)) and len(value.strip()) > 0
    
    def _handle_null_values(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """处理空值"""
        df = dataframe.copy()
        
        # 对于基因型字段，将空值标准化为None
        for field in self.GENETIC_FIELDS:
            if field in df.columns:
                df[field] = df[field].apply(
                    lambda x: None if pd.isna(x) or str(x).strip() in ['', 'N/A', 'NA', 'NULL'] else x
                )
        
        # 对于性别字段，将空值标准化为None
        if 'gender' in df.columns:
            df['gender'] = df['gender'].apply(
                lambda x: None if pd.isna(x) or str(x).strip() in ['', 'N/A', 'NA', 'NULL'] else x
            )
        
        return df
    
    def _remove_empty_rows(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """移除完全空白的行"""
        # 检查必需字段是否都为空
        if all(field in dataframe.columns for field in self.REQUIRED_FIELDS):
            mask = dataframe[self.REQUIRED_FIELDS].apply(
                lambda row: any(pd.notna(val) and str(val).strip() != '' for val in row), axis=1
            )
            return dataframe[mask]
        
        return dataframe
    
    def _format_error_summary(self, errors: List[Dict[str, Any]]) -> str:
        """格式化错误摘要"""
        if not errors:
            return "无错误"
        
        summary_lines = []
        error_by_type = {}
        
        for error in errors:
            error_type = error['error_type']
            if error_type not in error_by_type:
                error_by_type[error_type] = []
            error_by_type[error_type].append(error)
        
        for error_type, error_list in error_by_type.items():
            summary_lines.append(f"{error_type}: {len(error_list)} 个错误")
            for error in error_list[:3]:  # 只显示前3个具体错误
                row_info = f"第{error['row']}行" if isinstance(error['row'], int) and error['row'] > 0 else "结构错误"
                summary_lines.append(f"  - {row_info}: {error['message']}")
            
            if len(error_list) > 3:
                summary_lines.append(f"  - ... 还有 {len(error_list) - 3} 个类似错误")
        
        return '\n'.join(summary_lines)
    
    def validate_pigeon_data(self, pigeon_data: PigeonData) -> Tuple[bool, List[str]]:
        """
        验证单个信鸽数据
        Validate single pigeon data

        Args:
            pigeon_data: 信鸽数据对象

        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误列表)
        """
        errors = []

        # 检查必需字段（只检查关键字段）
        if not pigeon_data.item_no or not pigeon_data.item_no.strip():
            errors.append("检测序号不能为空")

        if not pigeon_data.pigeon_id or not pigeon_data.pigeon_id.strip():
            errors.append("信鸽足环号不能为空")

        # 性别字段允许为空或N/A
        # 不再强制要求性别字段

        # 检查基因型格式（允许为空，但如果有值则验证格式）
        genotype_fields = {
            'MSTN': pigeon_data.mstn,
            'F-KER': pigeon_data.f_ker,
            'LDHA': pigeon_data.ldha,
            'DRD4A': pigeon_data.drd4a,
            'DRD4B': pigeon_data.drd4b,
            'CRY1': pigeon_data.cry1
        }

        for field_name, value in genotype_fields.items():
            if value and value.strip():
                # 验证基因型格式（允许多种格式）
                cleaned_value = value.strip().upper()
                if not self._is_valid_genotype_format(cleaned_value):
                    errors.append(f"{field_name}基因型格式可能无效: {value}")

        # 检查性别值（允许更多格式）
        if pigeon_data.gender and pigeon_data.gender.strip():
            gender_value = pigeon_data.gender.strip().upper()
            valid_genders = ['雄', '雌', '公', '母', 'MALE', 'FEMALE', 'M', 'F', 'NAN', 'N/A', 'NA']
            if gender_value not in valid_genders:
                errors.append(f"性别值可能无效: {pigeon_data.gender}")

        return len(errors) == 0, errors

    def _is_valid_genotype_format(self, genotype: str) -> bool:
        """
        验证基因型格式是否有效

        Args:
            genotype: 基因型字符串

        Returns:
            bool: 是否为有效格式
        """
        if not genotype:
            return False

        # 允许的基因型格式模式
        valid_patterns = [
            r'^[ATCG]{2}$',           # 如: AA, GG, CT
            r'^[ATCG]/[ATCG]$',       # 如: A/A, C/T
            r'^[ATCG]{2}/[ATCG]{2}$', # 如: AG/TT, CC/TT
            r'^[ATCG]-[ATCG]$',       # 如: A-G, C-T
        ]

        for pattern in valid_patterns:
            if re.match(pattern, genotype):
                return True

        return False
    
    def check_duplicates(self, pigeon_data_list: List[PigeonData]) -> Dict[str, Any]:
        """
        检查重复数据
        Check duplicate data
        
        Args:
            pigeon_data_list: 信鸽数据列表
            
        Returns:
            dict: 重复检查结果
        """
        # 检查足环号重复
        pigeon_ids = [data.pigeon_id for data in pigeon_data_list if data.pigeon_id]
        pigeon_id_counts = {}
        
        for pigeon_id in pigeon_ids:
            pigeon_id_counts[pigeon_id] = pigeon_id_counts.get(pigeon_id, 0) + 1
        
        duplicates = {pid: count for pid, count in pigeon_id_counts.items() if count > 1}
        
        # 检查检测序号重复
        item_nos = [data.item_no for data in pigeon_data_list if data.item_no]
        item_no_counts = {}
        
        for item_no in item_nos:
            item_no_counts[item_no] = item_no_counts.get(item_no, 0) + 1
        
        duplicate_item_nos = {ino: count for ino, count in item_no_counts.items() if count > 1}
        
        return {
            'has_duplicates': len(duplicates) > 0 or len(duplicate_item_nos) > 0,
            'duplicates': duplicates,
            'duplicate_item_nos': duplicate_item_nos,
            'duplicate_count': len(duplicates) + len(duplicate_item_nos)
        }