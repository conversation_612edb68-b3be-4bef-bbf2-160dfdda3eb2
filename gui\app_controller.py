#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序控制器 - 连接GUI和后端逻辑
Application Controller - Connect GUI and backend logic
"""

import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import pandas as pd
import os
import sys
import logging
import threading
from typing import Optional, Dict, Any, List
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.main_integration import MainIntegration, IntegrationError
from core.data_models import PigeonData
from .main_window import MainWindow
from .components import ProgressDialog

logger = logging.getLogger(__name__)


class ApplicationController:
    """
    应用程序控制器
    Application Controller
    
    负责协调GUI界面和后端逻辑，处理用户操作和数据流转
    Responsible for coordinating GUI interface and backend logic, handling user operations and data flow
    """
    
    def __init__(self, output_dir: str = "output"):
        """
        初始化应用程序控制器
        Initialize application controller
        
        Args:
            output_dir: 输出目录
        """
        try:
            # 创建主窗口
            self.root = tk.Tk()
            self.main_window = MainWindow(self.root)
            
            # 初始化后端集成器
            self.integrator = MainIntegration(output_dir)
            
            # 设置回调函数
            self.main_window.set_generate_callbacks(
                cert_callback=self.generate_certificates,
                report_callback=self.generate_report
            )
            
            # 当前处理状态
            self.current_processing = False
            self.current_progress_dialog: Optional[ProgressDialog] = None
            
            logger.info("应用程序控制器初始化完成")
            
        except Exception as e:
            error_msg = f"应用程序控制器初始化失败: {str(e)}"
            logger.error(error_msg)
            raise IntegrationError(error_msg)
    
    def generate_certificates(self, data: pd.DataFrame, file_path: str):
        """
        生成证书
        Generate certificates
        
        Args:
            data: 数据DataFrame
            file_path: 源文件路径
        """
        if self.current_processing:
            messagebox.showwarning("警告", "正在处理中，请稍候...")
            return
        
        try:
            # 验证数据
            if data is None or data.empty:
                messagebox.showwarning("警告", "没有可处理的数据")
                return
            
            # 转换数据格式
            pigeon_data_list = self._convert_dataframe_to_pigeon_data(data)
            
            if not pigeon_data_list:
                messagebox.showwarning("警告", "没有有效的数据记录")
                return
            
            # 在后台线程中执行生成操作
            self._run_background_operation(
                operation_func=self._generate_certificates_background,
                operation_args=(pigeon_data_list, file_path),
                operation_name="生成证书",
                success_message="证书生成完成"
            )
            
        except Exception as e:
            error_msg = f"生成证书失败: {str(e)}"
            logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def generate_report(self, data: pd.DataFrame, file_path: str):
        """
        生成报告
        Generate report
        
        Args:
            data: 数据DataFrame
            file_path: 源文件路径
        """
        if self.current_processing:
            messagebox.showwarning("警告", "正在处理中，请稍候...")
            return
        
        try:
            # 验证数据
            if data is None or data.empty:
                messagebox.showwarning("警告", "没有可处理的数据")
                return
            
            # 转换数据格式
            pigeon_data_list = self._convert_dataframe_to_pigeon_data(data)
            
            if not pigeon_data_list:
                messagebox.showwarning("警告", "没有有效的数据记录")
                return
            
            # 在后台线程中执行生成操作
            self._run_background_operation(
                operation_func=self._generate_report_background,
                operation_args=(pigeon_data_list, file_path),
                operation_name="生成报告",
                success_message="报告生成完成"
            )
            
        except Exception as e:
            error_msg = f"生成报告失败: {str(e)}"
            logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def process_excel_file(self, excel_file_path: str):
        """
        处理Excel文件的完整工作流程
        Complete workflow for processing Excel file
        
        Args:
            excel_file_path: Excel文件路径
        """
        if self.current_processing:
            messagebox.showwarning("警告", "正在处理中，请稍候...")
            return
        
        try:
            # 验证文件存在
            if not Path(excel_file_path).exists():
                messagebox.showerror("错误", f"文件不存在: {excel_file_path}")
                return
            
            # 在后台线程中执行完整处理流程
            self._run_background_operation(
                operation_func=self._process_excel_file_background,
                operation_args=(excel_file_path,),
                operation_name="处理Excel文件",
                success_message="Excel文件处理完成"
            )
            
        except Exception as e:
            error_msg = f"处理Excel文件失败: {str(e)}"
            logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def _convert_dataframe_to_pigeon_data(self, data: pd.DataFrame) -> List[PigeonData]:
        """
        将DataFrame转换为PigeonData列表
        Convert DataFrame to PigeonData list
        
        Args:
            data: 数据DataFrame
            
        Returns:
            List[PigeonData]: 信鸽数据列表
        """
        try:
            pigeon_data_list = []

            # 检查列名格式，支持中文和英文列名
            columns = data.columns.tolist()
            logger.info(f"DataFrame列名: {columns}")

            # 定义列名映射（中文->英文）
            column_mapping = {
                '检测序号': 'item_no',
                '信鸽足环号': 'pigeon_id',
                '性别': 'gender',
                'MSTN': 'mstn',
                'F-KER': 'f_ker',
                'LDHA': 'ldha',
                'DRD4A': 'drd4a',
                'DRD4B': 'drd4b',
                'CRY1': 'cry1'
            }

            for row_num, (_, row) in enumerate(data.iterrows()):
                try:
                    # 智能获取字段值，支持中文和英文列名
                    def get_field_value(field_name, default=''):
                        # 先尝试英文列名
                        if field_name in row:
                            value = row.get(field_name, default)
                        else:
                            # 再尝试中文列名
                            chinese_name = None
                            for cn, en in column_mapping.items():
                                if en == field_name:
                                    chinese_name = cn
                                    break
                            value = row.get(chinese_name, default) if chinese_name else default

                        # 处理NaN和None值
                        if pd.isna(value) or value is None:
                            return default
                        return str(value)

                    # 创建PigeonData对象
                    pigeon_data = PigeonData(
                        item_no=get_field_value('item_no', f'{row_num+1:03d}'),
                        pigeon_id=get_field_value('pigeon_id', ''),
                        gender=get_field_value('gender', ''),
                        mstn=get_field_value('mstn', ''),
                        f_ker=get_field_value('f_ker', ''),
                        ldha=get_field_value('ldha', ''),
                        drd4a=get_field_value('drd4a', ''),
                        drd4b=get_field_value('drd4b', ''),
                        cry1=get_field_value('cry1', '')
                    )

                    pigeon_data_list.append(pigeon_data)

                except Exception as e:
                    logger.warning(f"跳过第{row_num+1}行数据，转换失败: {e}")
                    continue

            logger.info(f"成功转换 {len(pigeon_data_list)} 条数据记录")
            return pigeon_data_list

        except Exception as e:
            error_msg = f"数据转换失败: {str(e)}"
            logger.error(error_msg)
            raise IntegrationError(error_msg)
    
    def _run_background_operation(self, operation_func, operation_args, 
                                 operation_name: str, success_message: str):
        """
        在后台线程中运行操作
        Run operation in background thread
        
        Args:
            operation_func: 操作函数
            operation_args: 操作参数
            operation_name: 操作名称
            success_message: 成功消息
        """
        try:
            # 设置处理状态
            self.current_processing = True
            
            # 创建进度对话框
            self.current_progress_dialog = ProgressDialog(
                self.root, 
                title=operation_name,
                message="正在初始化..."
            )
            
            # 定义进度回调函数
            def progress_callback(stage: str, current: int, total: int):
                if self.current_progress_dialog:
                    self.current_progress_dialog.show_progress(current, total, stage)
            
            # 定义后台操作函数
            def background_operation():
                try:
                    # 执行操作
                    result = operation_func(*operation_args, progress_callback)
                    
                    # 在主线程中处理结果
                    self.root.after(0, lambda: self._handle_operation_success(result, success_message))
                    
                except Exception as e:
                    # 在主线程中处理错误
                    self.root.after(0, lambda error=e: self._handle_operation_error(error, operation_name))
            
            # 启动后台线程
            thread = threading.Thread(target=background_operation, daemon=True)
            thread.start()
            
            # 进度对话框已在初始化时显示，无需调用show()
            
        except Exception as e:
            self._handle_operation_error(e, operation_name)
    
    def _generate_certificates_background(self, pigeon_data_list: List[PigeonData], 
                                        file_path: str, progress_callback) -> Dict[str, Any]:
        """
        后台生成证书
        Background certificate generation
        
        Args:
            pigeon_data_list: 信鸽数据列表
            file_path: 源文件路径
            progress_callback: 进度回调函数
            
        Returns:
            dict: 生成结果
        """
        try:
            # 创建输出目录
            progress_callback("创建输出目录", 1, 6)
            output_dir = self.integrator.file_manager.create_output_folder(file_path, "证书生成")
            
            # 数据验证
            progress_callback("验证数据", 2, 6)
            validation_result = self.integrator._validate_data(pigeon_data_list)
            
            if not validation_result['success']:
                raise IntegrationError(f"数据验证失败: {validation_result.get('errors', [])}")
            
            # 生成证书
            progress_callback("生成证书", 3, 6)
            certificates_result = self.integrator._generate_certificates(pigeon_data_list, output_dir)
            
            if not certificates_result['success']:
                raise IntegrationError(f"证书生成失败: {certificates_result.get('error', '未知错误')}")
            
            # 保存文件
            progress_callback("保存文件", 4, 6)
            save_result = self.integrator._save_files(
                certificates_result, 
                {'success': False, 'skipped': True}, 
                output_dir
            )
            
            if not save_result['success']:
                raise IntegrationError(f"文件保存失败: {save_result.get('error', '未知错误')}")
            
            progress_callback("完成", 6, 6)
            
            return {
                'success': True,
                'output_dir': str(output_dir),
                'certificates_count': certificates_result.get('generated_count', 0),
                'saved_files': save_result.get('saved_files', [])
            }
            
        except Exception as e:
            logger.error(f"后台生成证书失败: {e}")
            raise e
    
    def _generate_report_background(self, pigeon_data_list: List[PigeonData], 
                                  file_path: str, progress_callback) -> Dict[str, Any]:
        """
        后台生成报告
        Background report generation
        
        Args:
            pigeon_data_list: 信鸽数据列表
            file_path: 源文件路径
            progress_callback: 进度回调函数
            
        Returns:
            dict: 生成结果
        """
        try:
            # 创建输出目录
            progress_callback("创建输出目录", 1, 6)
            output_dir = self.integrator.file_manager.create_output_folder(file_path, "报告生成")
            
            # 数据验证
            progress_callback("验证数据", 2, 6)
            validation_result = self.integrator._validate_data(pigeon_data_list)
            
            if not validation_result['success']:
                raise IntegrationError(f"数据验证失败: {validation_result.get('errors', [])}")
            
            # 生成报告
            progress_callback("生成报告", 3, 6)
            report_result = self.integrator._generate_report(pigeon_data_list, output_dir)
            
            if not report_result['success']:
                raise IntegrationError(f"报告生成失败: {report_result.get('error', '未知错误')}")
            
            # 保存文件
            progress_callback("保存文件", 4, 6)
            save_result = self.integrator._save_files(
                {'success': False, 'skipped': True}, 
                report_result, 
                output_dir
            )
            
            if not save_result['success']:
                raise IntegrationError(f"文件保存失败: {save_result.get('error', '未知错误')}")
            
            progress_callback("完成", 6, 6)
            
            return {
                'success': True,
                'output_dir': str(output_dir),
                'saved_files': save_result.get('saved_files', [])
            }
            
        except Exception as e:
            logger.error(f"后台生成报告失败: {e}")
            raise e
    
    def _process_excel_file_background(self, excel_file_path: str, progress_callback) -> Dict[str, Any]:
        """
        后台处理Excel文件
        Background Excel file processing
        
        Args:
            excel_file_path: Excel文件路径
            progress_callback: 进度回调函数
            
        Returns:
            dict: 处理结果
        """
        try:
            # 使用主集成器处理Excel文件
            result = self.integrator.process_excel_file(excel_file_path, progress_callback)
            
            return result
            
        except Exception as e:
            logger.error(f"后台处理Excel文件失败: {e}")
            raise e
    
    def _handle_operation_success(self, result: Dict[str, Any], success_message: str):
        """
        处理操作成功
        Handle operation success
        
        Args:
            result: 操作结果
            success_message: 成功消息
        """
        try:
            # 关闭进度对话框
            if self.current_progress_dialog:
                self.current_progress_dialog.close()
                self.current_progress_dialog = None
            
            # 重置处理状态
            self.current_processing = False
            
            # 更新状态栏
            self.main_window.update_status(success_message)
            
            # 显示成功消息
            message_parts = [success_message]
            
            if 'output_dir' in result:
                message_parts.append(f"输出目录: {result['output_dir']}")
            
            if 'certificates_count' in result:
                message_parts.append(f"生成证书: {result['certificates_count']} 个")
            
            if 'saved_files' in result:
                saved_files = result['saved_files']
                if saved_files:
                    message_parts.append(f"保存文件: {len(saved_files)} 个")
                    for file_info in saved_files[:3]:  # 只显示前3个文件
                        file_name = Path(file_info['path']).name
                        message_parts.append(f"  - {file_name}")
                    
                    if len(saved_files) > 3:
                        message_parts.append(f"  ... 还有 {len(saved_files) - 3} 个文件")
            
            success_text = "\n".join(message_parts)
            
            # 询问是否打开输出目录
            if 'output_dir' in result:
                response = messagebox.askyesno(
                    "操作完成", 
                    f"{success_text}\n\n是否打开输出目录？"
                )
                
                if response:
                    self._open_output_directory(result['output_dir'])
            else:
                messagebox.showinfo("操作完成", success_text)
            
            logger.info(f"操作成功完成: {success_message}")
            
        except Exception as e:
            logger.error(f"处理操作成功结果时出错: {e}")
    
    def _handle_operation_error(self, error: Exception, operation_name: str):
        """
        处理操作错误
        Handle operation error
        
        Args:
            error: 错误异常
            operation_name: 操作名称
        """
        try:
            # 关闭进度对话框
            if self.current_progress_dialog:
                self.current_progress_dialog.close()
                self.current_progress_dialog = None
            
            # 重置处理状态
            self.current_processing = False
            
            # 更新状态栏
            self.main_window.update_status(f"{operation_name}失败")
            
            # 显示错误消息
            error_msg = f"{operation_name}失败:\n{str(error)}"
            messagebox.showerror("操作失败", error_msg)
            
            logger.error(f"{operation_name}失败: {error}")
            
        except Exception as e:
            logger.error(f"处理操作错误时出错: {e}")
    
    def _open_output_directory(self, output_dir: str):
        """
        打开输出目录
        Open output directory
        
        Args:
            output_dir: 输出目录路径
        """
        try:
            import subprocess
            import platform
            
            system = platform.system()
            
            if system == "Windows":
                os.startfile(output_dir)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", output_dir])
            else:  # Linux
                subprocess.run(["xdg-open", output_dir])
                
            logger.info(f"打开输出目录: {output_dir}")
            
        except Exception as e:
            logger.warning(f"无法打开输出目录: {e}")
            messagebox.showwarning("警告", f"无法打开输出目录:\n{output_dir}")
    
    def validate_environment(self) -> bool:
        """
        验证运行环境
        Validate runtime environment
        
        Returns:
            bool: 环境是否有效
        """
        try:
            # 验证后端环境
            env_result = self.integrator.validate_environment()
            
            if not env_result['success']:
                # 显示环境问题
                error_parts = ["运行环境检查发现问题:"]
                
                for check_name, check_result in env_result['checks'].items():
                    if not check_result['success']:
                        error_parts.append(f"• {check_name}: 失败")
                        if 'error' in check_result:
                            error_parts.append(f"  错误: {check_result['error']}")
                
                if env_result['warnings']:
                    error_parts.append("\n警告:")
                    for warning in env_result['warnings']:
                        error_parts.append(f"• {warning}")
                
                if env_result['errors']:
                    error_parts.append("\n错误:")
                    for error in env_result['errors']:
                        error_parts.append(f"• {error}")
                
                error_text = "\n".join(error_parts)
                
                response = messagebox.askyesnocancel(
                    "环境检查", 
                    f"{error_text}\n\n是否继续运行程序?\n（选择'否'退出程序）"
                )
                
                if response is None:  # Cancel
                    return False
                elif response is False:  # No
                    return False
                # Yes - 继续运行
            
            return True
            
        except Exception as e:
            logger.error(f"环境验证失败: {e}")
            messagebox.showerror("环境验证失败", f"无法验证运行环境:\n{str(e)}")
            return False
    
    def show_system_info(self):
        """
        显示系统信息
        Show system information
        """
        try:
            sys_info = self.integrator.get_system_info()
            
            info_parts = [
                "=== 系统信息 ===",
                f"操作系统: {sys_info['platform']['system']} {sys_info['platform']['release']}",
                f"Python版本: {sys_info['python']['version'].split()[0]}",
                "",
                "=== 模块状态 ==="
            ]
            
            for module_name, status in sys_info['modules'].items():
                info_parts.append(f"{module_name}: {status}")
            
            info_parts.extend([
                "",
                "=== 配置信息 ==="
            ])
            
            for config_key, config_value in sys_info['configuration'].items():
                info_parts.append(f"{config_key}: {config_value}")
            
            info_text = "\n".join(info_parts)
            
            # 创建信息显示窗口
            info_window = tk.Toplevel(self.root)
            info_window.title("系统信息")
            info_window.geometry("600x400")
            info_window.resizable(True, True)
            
            # 创建文本框显示信息
            text_frame = ttk.Frame(info_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            text_widget.insert(tk.END, info_text)
            text_widget.config(state=tk.DISABLED)
            
            # 关闭按钮
            button_frame = ttk.Frame(info_window)
            button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
            
            close_btn = ttk.Button(button_frame, text="关闭", command=info_window.destroy)
            close_btn.pack(side=tk.RIGHT)
            
        except Exception as e:
            logger.error(f"显示系统信息失败: {e}")
            messagebox.showerror("错误", f"无法显示系统信息:\n{str(e)}")
    
    def run(self):
        """
        运行应用程序
        Run application
        """
        try:
            # 验证环境
            if not self.validate_environment():
                logger.info("用户取消运行或环境验证失败")
                return
            
            # 运行主窗口
            logger.info("启动证书生成工具")
            self.main_window.run()
            
        except Exception as e:
            logger.error(f"运行应用程序失败: {e}")
            messagebox.showerror("启动失败", f"无法启动应用程序:\n{str(e)}")


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 创建并运行应用程序控制器
        app_controller = ApplicationController()
        app_controller.run()
        
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        # 如果GUI还没有创建，使用print输出错误
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()