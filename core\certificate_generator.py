#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
证书生成器模块 - 生成单页PDF证书
Certificate Generator Module - Generate single page PDF certificates
"""

import os
import logging
from typing import List, Dict, Any, Optional, Union, Callable
from pathlib import Path
import tempfile
import shutil

try:
    import weasyprint
    from weasyprint import HTML, CSS
    WEASYPRINT_AVAILABLE = True
except (ImportError, OSError):
    WEASYPRINT_AVAILABLE = False
    HTML = None
    CSS = None

try:
    from PyPDF2 import PdfMerger, PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    PdfMerger = None
    PdfReader = None

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.units import mm
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from .template_manager import TemplateManager
from .image_processor import ImageProcessor
from .data_models import PigeonData
from .font_manager import font_manager

logger = logging.getLogger(__name__)


class CertificateGenerationError(Exception):
    """证书生成错误异常"""
    pass


class CertificateGenerator:
    """
    证书生成器
    Certificate Generator
    
    负责生成单页PDF证书和合并多个证书
    Responsible for generating single page PDF certificates and merging multiple certificates
    """
    
    def __init__(self, template_manager: Optional[TemplateManager] = None,
                 image_processor: Optional[ImageProcessor] = None):
        """
        初始化证书生成器
        Initialize certificate generator

        Args:
            template_manager: 模板管理器实例
            image_processor: 图像处理器实例
        """
        # 不在初始化时检查PDF库，而是在实际使用时检查
        # 这样可以让程序正常启动，只在需要生成PDF时才报错
        self.pdf_libraries_available = WEASYPRINT_AVAILABLE or REPORTLAB_AVAILABLE
        self.pypdf2_available = PYPDF2_AVAILABLE

        if not self.pdf_libraries_available:
            logger.warning("PDF生成库未安装，PDF生成功能将不可用")

        if not self.pypdf2_available:
            logger.warning("PyPDF2未安装，PDF合并功能将不可用")
        
        # 优先使用 WeasyPrint (支持HTML模板)，ReportLab作为备用
        self.use_weasyprint = WEASYPRINT_AVAILABLE
        
        # 初始化依赖组件
        self.template_manager = template_manager or TemplateManager()
        self.image_processor = image_processor or ImageProcessor()
        
        # PDF生成配置
        self.pdf_config = {
            'page_size': 'A4',
            'orientation': 'portrait',
            'margin': '20mm',
            'encoding': 'utf-8'
        }
        
        # 中文字体配置
        self.font_config = self._setup_font_config()
        
        logger.info("证书生成器初始化完成")
    
    def _setup_font_config(self) -> Dict[str, Any]:
        """
        设置中文字体配置
        Setup Chinese font configuration
        
        Returns:
            dict: 字体配置字典
        """
        font_config = {
            'font_family': font_manager.get_font_family_css(),
            'font_size': '12pt',
            'line_height': '1.5',
            'chinese_font': font_manager.get_chinese_font(bold=False),
            'chinese_bold_font': font_manager.get_chinese_font(bold=True)
        }

        logger.info(f"证书字体配置: {font_config}")
        return font_config
    
    def _create_css_styles(self) -> str:
        """
        创建PDF样式CSS
        Create CSS styles for PDF
        
        Returns:
            str: CSS样式字符串
        """
        css_styles = f"""
        @page {{
            size: {self.pdf_config['page_size']};
            margin: {self.pdf_config['margin']};
        }}
        
        body {{
            font-family: {self.font_config['font_family']};
            font-size: {self.font_config['font_size']};
            line-height: {self.font_config['line_height']};
            margin: 0;
            padding: 0;
        }}
        
        .certificate {{
            width: 100%;
            height: 100%;
            display: block;
        }}
        
        .certificate-header {{
            text-align: center;
            margin-bottom: 20px;
        }}
        
        .certificate-title {{
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        
        .certificate-content {{
            margin: 20px 0;
        }}
        
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        
        .data-table th,
        .data-table td {{
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }}
        
        .data-table th {{
            background-color: #f0f0f0;
            font-weight: bold;
        }}
        
        .signature-section {{
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }}
        
        .seal-image,
        .signature-image {{
            max-width: 120px;
            max-height: 120px;
        }}
        
        .date-section {{
            text-align: right;
            margin-top: 20px;
        }}
        """
        
        return css_styles
    
    def _prepare_certificate_data(self, pigeon_data: PigeonData, 
                                 additional_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        准备证书数据
        Prepare certificate data
        
        Args:
            pigeon_data: 信鸽数据
            additional_data: 额外数据
            
        Returns:
            dict: 准备好的证书数据
        """
        # 基础数据
        cert_data = pigeon_data.to_dict()
        
        # 添加额外数据
        if additional_data:
            cert_data.update(additional_data)
        
        # 添加图片数据
        try:
            image_context = self.image_processor.create_placeholder_context()
            cert_data.update(image_context)
        except Exception as e:
            logger.warning(f"获取图片数据失败: {e}")
            cert_data['seal_image'] = None
            cert_data['signature_image'] = None
        
        # 添加基因型评价
        cert_data.update(self._evaluate_genotypes(pigeon_data))
        
        return cert_data
    
    def _evaluate_genotypes(self, pigeon_data: PigeonData) -> Dict[str, str]:
        """
        评价基因型
        Evaluate genotypes
        
        Args:
            pigeon_data: 信鸽数据
            
        Returns:
            dict: 基因型评价结果
        """
        evaluations = {}
        
        # 基因型评价规则
        evaluation_rules = {
            'mstn': {
                'CC': '优',
                'CT': '佳', 
                'TT': '普通'
            },
            'f_ker': {
                'TT': '优',
                'TC': '佳',
                'CC': '普通'
            },
            'ldha': {
                'AA': '优',
                'AB': '佳',
                'BB': '普通'
            },
            'drd4a': {
                'GG': '优',
                'GA': '佳',
                'AA': '普通'
            },
            'drd4b': {
                'GG': '优',
                'GA': '佳',
                'AA': '普通'
            },
            'cry1': {
                'CC': '优',
                'CT': '佳',
                'TT': '普通'
            }
        }
        
        # 应用评价规则
        for gene, rules in evaluation_rules.items():
            gene_value = getattr(pigeon_data, gene, None)
            if gene_value and gene_value in rules:
                evaluations[f'{gene}_evaluation'] = rules[gene_value]
            else:
                evaluations[f'{gene}_evaluation'] = '未知'
        
        return evaluations
    
    def _generate_pdf_with_reportlab_from_template(self, cert_data: Dict[str, Any], template_name: str) -> bytes:
        """
        使用ReportLab生成PDF，但基于HTML模板的数据结构
        Generate PDF using ReportLab, but based on HTML template data structure

        Args:
            cert_data: 证书数据
            template_name: 模板名称（用于确定样式）

        Returns:
            bytes: PDF字节数据
        """
        from io import BytesIO

        # 创建字节流
        buffer = BytesIO()

        # 创建PDF文档
        doc = SimpleDocTemplate(buffer, pagesize=A4,
                               rightMargin=20*mm, leftMargin=20*mm,
                               topMargin=20*mm, bottomMargin=20*mm)

        # 获取样式
        styles = getSampleStyleSheet()

        # 创建自定义样式（使用中文字体，模仿HTML模板样式）
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=32,
            spaceAfter=10,
            alignment=1,  # 居中
            fontName=self.font_config['chinese_bold_font'],
            textColor=colors.HexColor('#1a3c6c'),
            letterSpacing=8
        )

        sub_title_style = ParagraphStyle(
            'SubTitle',
            parent=styles['Heading2'],
            fontSize=24,
            spaceAfter=15,
            alignment=1,  # 居中
            fontName=self.font_config['chinese_bold_font'],
            textColor=colors.HexColor('#c00')
        )

        report_number_style = ParagraphStyle(
            'ReportNumber',
            parent=styles['Normal'],
            fontSize=18,
            spaceAfter=25,
            alignment=2,  # 右对齐
            fontName=self.font_config['chinese_bold_font']
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=16,
            spaceAfter=12,
            fontName=self.font_config['chinese_font'],
            leading=28.8  # 1.8倍行高
        )

        section_title_style = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading3'],
            fontSize=18,
            spaceAfter=10,
            spaceBefore=15,
            fontName=self.font_config['chinese_bold_font'],
            textColor=colors.HexColor('#1a3c6c'),
            leftIndent=10,
            borderLeft=4,
            borderLeftColor=colors.HexColor('#1a3c6c'),
            borderLeftWidth=4
        )

        # 构建PDF内容
        story = []

        # 标题部分
        story.append(Paragraph("Pigeon Dna Certing Report", title_style))
        story.append(Paragraph("赛鸽全方位基因型鉴定报告书", sub_title_style))
        story.append(Paragraph(f"报告编号：{cert_data.get('report_number', '')}", report_number_style))

        # 信息表格
        info_data = [
            ['送检编号', cert_data.get('item_no', ''), '采样日期', cert_data.get('sample_date', ''), '收样日期', cert_data.get('receive_date', '')],
            ['脚环号', cert_data.get('pigeon_id', ''), '性别', cert_data.get('gender', ''), '样品类型', '羽毛'],
            ['送检人', cert_data.get('submitter', ''), '', '', '打印日期', cert_data.get('print_date', '')]
        ]

        info_table = Table(info_data, colWidths=[25*mm, 35*mm, 25*mm, 35*mm, 25*mm, 35*mm])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), self.font_config['chinese_font']),
            ('FONTSIZE', (0, 0), (-1, -1), 16),
            ('GRID', (0, 0), (-1, -1), 2, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),  # 第1列
            ('BACKGROUND', (2, 0), (2, -1), colors.HexColor('#f0f0f0')),  # 第3列
            ('BACKGROUND', (4, 0), (4, -1), colors.HexColor('#f0f0f0')),  # 第5列
            ('FONTNAME', (0, 0), (0, -1), self.font_config['chinese_bold_font']),  # 第1列粗体
            ('FONTNAME', (2, 0), (2, -1), self.font_config['chinese_bold_font']),  # 第3列粗体
            ('FONTNAME', (4, 0), (4, -1), self.font_config['chinese_bold_font']),  # 第5列粗体
            ('SPAN', (1, 2), (3, 2)),  # 送检人跨列
        ]))

        story.append(info_table)
        story.append(Spacer(1, 30))

        # 鉴定方法
        story.append(Paragraph("鉴定方法：", section_title_style))
        method_text = "采集羽毛的毛囊细胞，提取赛鸽的DNA，利用PCR和测序方法，分析赛鸽的MSTR、F-KER、LDHA、DRD4、CRY1和CASK基因中DNA的序列，进行关键位点基因型的检测。"
        story.append(Paragraph(method_text, normal_style))
        story.append(Spacer(1, 25))

        # 检测结果
        story.append(Paragraph("检测结果：", section_title_style))

        # 结果表格
        gene_data = [
            ['基因名称', '基因型', '判定'],
            [f'肌肉调控基因（MSTR）\n[肌肉指标]', cert_data.get('mstn', ''), cert_data.get('mstn_evaluation', '')],
            [f'角羽蛋白基因（F-KER）\n[羽翼指标]', cert_data.get('f_ker', ''), cert_data.get('f_ker_evaluation', '')],
            [f'乳酸脱氢酶-A（LDHA）\n[耐力指标]', cert_data.get('ldha', ''), cert_data.get('ldha_evaluation', '')],
            [f'多巴胺D4受体（DRD4）\n[归巢性指标]', f"{cert_data.get('drd4a', '')}{cert_data.get('drd4b', '')}", cert_data.get('drd4_evaluation', '')],
            [f'隐花色素基因（CRY1）\n[磁场感应指标]', cert_data.get('cry1', ''), cert_data.get('cry1_evaluation', '')]
        ]

        gene_table = Table(gene_data, colWidths=[60*mm, 50*mm, 30*mm])
        gene_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), self.font_config['chinese_font']),
            ('FONTSIZE', (0, 0), (-1, -1), 16),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1a3c6c')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), self.font_config['chinese_bold_font']),
            ('FONTSIZE', (0, 0), (-1, 0), 16),
            # 交替行背景色
            ('BACKGROUND', (0, 2), (-1, 2), colors.HexColor('#f9f9f9')),
            ('BACKGROUND', (0, 4), (-1, 4), colors.HexColor('#f9f9f9')),
            ('BACKGROUND', (0, 6), (-1, 6), colors.HexColor('#f9f9f9')),
        ]))

        story.append(gene_table)
        story.append(Spacer(1, 30))

        # 鉴定说明
        story.append(Paragraph("鉴定说明：", section_title_style))
        explanation_text = "长久以来，拥有优良的血液—重载认为是影响比赛成绩的重要因子，所谓优良的血液，就是指特定可以遗传的基因型。近年的研究显示，乳酸去氢酶A（LDHA）、多巴胺D4受体（DRD4）、肌肉调控（MSTR）、羽翼蛋白（F-KER）、隐花色素基因（CRY1）、丝氨酸蛋白激酶（CASK）是六个先天上会影响比赛成绩的重要途径因子。"
        story.append(Paragraph(explanation_text, normal_style))
        story.append(Spacer(1, 20))

        # 判定说明
        criteria_title_style = ParagraphStyle(
            'CriteriaTitle',
            parent=styles['Heading3'],
            fontSize=18,
            spaceAfter=10,
            spaceBefore=20,
            fontName=self.font_config['chinese_bold_font'],
            textColor=colors.HexColor('#c00')
        )

        criteria_style = ParagraphStyle(
            'Criteria',
            parent=styles['Normal'],
            fontSize=15,
            spaceAfter=8,
            fontName=self.font_config['chinese_font'],
            leftIndent=20,
            firstLineIndent=-20,
            leading=24
        )

        story.append(Paragraph("判定说明：", criteria_title_style))

        criteria_texts = [
            "(1) MSTR基因型分为三个等级：基因型 TT 判定为【优】；基因型为 CT 判定为【佳】；基因型为 CC 判定为【普通】",
            "(2) F-KER基因型分为三个等级：基因型 TT 判定为【优】；基因型为 GT 判定为【佳】；基因型为 GG 判定为【普通】",
            "(3) LDHA基因型分为三个等级：基因型 AA 判定为【优】；基因型为 AG 判定为【佳】；基因型为 GG 判定为【普通】",
            "(4) DRD4 基因型分为五个等级：基因型 CTCT 判定为【优】；基因型为 CTCC、CCCT 判定为【佳】；基因型为 CCCC 判定为【普通】",
            "(5) CRY1基因型分为四个等级：基因型TT/TT、AG/TT判定为【优】；基因型为AG/AG判定为【佳】；基因型为AG/TT判定为【普通】"
        ]

        for criteria_text in criteria_texts:
            story.append(Paragraph(criteria_text, criteria_style))

        story.append(Spacer(1, 40))

        # 签名区域
        signature_text = Paragraph("鉴定单位：________________", normal_style)
        story.append(signature_text)

        # 构建PDF
        doc.build(story)

        # 获取PDF字节数据
        pdf_bytes = buffer.getvalue()
        buffer.close()

        return pdf_bytes

    def _generate_pdf_with_reportlab(self, cert_data: Dict[str, Any]) -> bytes:
        """
        使用ReportLab生成PDF
        Generate PDF using ReportLab
        
        Args:
            cert_data: 证书数据
            
        Returns:
            bytes: PDF字节数据
        """
        from io import BytesIO
        
        # 创建字节流
        buffer = BytesIO()
        
        # 创建PDF文档
        doc = SimpleDocTemplate(buffer, pagesize=A4, 
                               rightMargin=20*mm, leftMargin=20*mm,
                               topMargin=20*mm, bottomMargin=20*mm)
        
        # 获取样式
        styles = getSampleStyleSheet()
        
        # 创建自定义样式（使用中文字体）
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1,  # 居中
            fontName=self.font_config['chinese_bold_font']
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            fontName=self.font_config['chinese_font']
        )
        
        # 构建内容
        story = []
        
        # 标题
        title = Paragraph("信鸽基因检测证书", title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # 基本信息
        basic_info = [
            ['检测序号:', cert_data.get('item_no', '')],
            ['信鸽足环号:', cert_data.get('pigeon_id', '')],
            ['性别:', cert_data.get('gender', '')],
            ['报告编号:', cert_data.get('report_number', '')],
            ['送检人:', cert_data.get('submitter', '')],
            ['采样日期:', cert_data.get('sample_date', '')],
            ['接收日期:', cert_data.get('receive_date', '')],
            ['打印日期:', cert_data.get('print_date', '')]
        ]
        
        basic_table = Table(basic_info, colWidths=[60*mm, 80*mm])
        basic_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        
        story.append(basic_table)
        story.append(Spacer(1, 20))
        
        # 基因检测结果
        gene_title = Paragraph("基因检测结果", ParagraphStyle(
            'GeneTitle', parent=styles['Heading2'], fontSize=16, spaceAfter=12
        ))
        story.append(gene_title)
        
        gene_data = [
            ['基因', '基因型', '评价'],
            ['MSTN', cert_data.get('mstn', ''), cert_data.get('mstn_evaluation', '')],
            ['F-KER', cert_data.get('f_ker', ''), cert_data.get('f_ker_evaluation', '')],
            ['LDHA', cert_data.get('ldha', ''), cert_data.get('ldha_evaluation', '')],
            ['DRD4a', cert_data.get('drd4a', ''), cert_data.get('drd4a_evaluation', '')],
            ['DRD4b', cert_data.get('drd4b', ''), cert_data.get('drd4b_evaluation', '')],
            ['CRY1', cert_data.get('cry1', ''), cert_data.get('cry1_evaluation', '')]
        ]
        
        gene_table = Table(gene_data, colWidths=[40*mm, 40*mm, 40*mm])
        gene_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), self.font_config['chinese_font']),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('FONTNAME', (0, 0), (-1, 0), self.font_config['chinese_bold_font']),
        ]))
        
        story.append(gene_table)
        story.append(Spacer(1, 40))
        
        # 签名区域
        signature_text = Paragraph("检测机构签章：________________    日期：________________", normal_style)
        story.append(signature_text)
        
        # 构建PDF
        doc.build(story)
        
        # 获取PDF字节数据
        pdf_bytes = buffer.getvalue()
        buffer.close()
        
        return pdf_bytes
    
    def _generate_pdf_with_weasyprint(self, cert_data: Dict[str, Any], template_name: str) -> bytes:
        """
        使用WeasyPrint生成PDF
        Generate PDF using WeasyPrint
        
        Args:
            cert_data: 证书数据
            template_name: 模板名称
            
        Returns:
            bytes: PDF字节数据
        """
        # 渲染HTML模板
        html_content = self.template_manager.render_template(template_name, cert_data)
        
        # 创建CSS样式
        css_content = self._create_css_styles()
        
        # 生成PDF
        html_doc = HTML(string=html_content, encoding=self.pdf_config['encoding'])
        css_doc = CSS(string=css_content)
        
        # 生成PDF字节数据
        pdf_bytes = html_doc.write_pdf(stylesheets=[css_doc])
        
        return pdf_bytes

    def generate_single_certificate(self, pigeon_data: PigeonData,
                                   template_name: str = 'certificate_template.html',
                                   additional_data: Optional[Dict[str, Any]] = None,
                                   output_path: Optional[Union[str, Path]] = None) -> bytes:
        """
        生成单页证书PDF
        Generate single certificate PDF

        Args:
            pigeon_data: 信鸽数据
            template_name: 模板文件名
            additional_data: 额外数据
            output_path: 输出路径，如果为None则返回PDF字节数据

        Returns:
            bytes: PDF字节数据

        Raises:
            CertificateGenerationError: 证书生成失败
        """
        # 检查PDF生成库是否可用
        if not self.pdf_libraries_available:
            raise CertificateGenerationError("PDF生成库未安装，请运行: pip install weasyprint 或 pip install reportlab")

        try:
            # 准备证书数据
            cert_data = self._prepare_certificate_data(pigeon_data, additional_data)
            
            # 根据可用库选择生成方法（优先使用WeasyPrint以支持HTML模板）
            if self.use_weasyprint:
                pdf_bytes = self._generate_pdf_with_weasyprint(cert_data, template_name)
            elif REPORTLAB_AVAILABLE:
                # 使用基于模板数据结构的ReportLab方法
                pdf_bytes = self._generate_pdf_with_reportlab_from_template(cert_data, template_name)
            else:
                raise CertificateGenerationError("没有可用的PDF生成库")
            
            # 如果指定了输出路径，保存文件
            if output_path:
                output_path = Path(output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(output_path, 'wb') as f:
                    f.write(pdf_bytes)
                
                logger.info(f"单页证书已保存: {output_path}")
            
            logger.info(f"单页证书生成成功: 信鸽ID {pigeon_data.pigeon_id}")
            return pdf_bytes
            
        except Exception as e:
            error_msg = f"生成单页证书失败: {str(e)}"
            logger.error(error_msg)
            raise CertificateGenerationError(error_msg)
    
    def generate_batch_certificates(self, pigeon_data_list: List[PigeonData],
                                   template_name: str = 'certificate_template.html',
                                   additional_data: Optional[Dict[str, Any]] = None,
                                   progress_callback: Optional[Callable[[int, int], None]] = None) -> List[bytes]:
        """
        批量生成证书PDF
        Generate batch certificates PDF
        
        Args:
            pigeon_data_list: 信鸽数据列表
            template_name: 模板文件名
            additional_data: 额外数据
            progress_callback: 进度回调函数 (current, total)
            
        Returns:
            List[bytes]: PDF字节数据列表
            
        Raises:
            CertificateGenerationError: 批量生成失败
        """
        try:
            pdf_list = []
            total_count = len(pigeon_data_list)
            
            logger.info(f"开始批量生成证书: {total_count} 个")
            
            for i, pigeon_data in enumerate(pigeon_data_list):
                try:
                    # 生成单个证书
                    pdf_bytes = self.generate_single_certificate(
                        pigeon_data, template_name, additional_data
                    )
                    pdf_list.append(pdf_bytes)
                    
                    # 调用进度回调
                    if progress_callback:
                        progress_callback(i + 1, total_count)
                    
                except Exception as e:
                    logger.error(f"生成证书失败 (信鸽ID: {pigeon_data.pigeon_id}): {e}")
                    # 继续处理其他证书
                    continue
            
            logger.info(f"批量证书生成完成: 成功 {len(pdf_list)}/{total_count}")
            return pdf_list
            
        except Exception as e:
            error_msg = f"批量生成证书失败: {str(e)}"
            logger.error(error_msg)
            raise CertificateGenerationError(error_msg)
    
    def merge_certificates(self, pdf_bytes_list: List[bytes],
                          output_path: Union[str, Path]) -> Path:
        """
        合并多个证书PDF
        Merge multiple certificate PDFs
        
        Args:
            pdf_bytes_list: PDF字节数据列表
            output_path: 输出文件路径
            
        Returns:
            Path: 合并后的PDF文件路径
            
        Raises:
            CertificateGenerationError: 合并失败
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建PDF合并器
            merger = PdfMerger()
            
            # 创建临时目录存储PDF文件
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_files = []
                
                # 将字节数据写入临时文件
                for i, pdf_bytes in enumerate(pdf_bytes_list):
                    temp_file = Path(temp_dir) / f"cert_{i:04d}.pdf"
                    with open(temp_file, 'wb') as f:
                        f.write(pdf_bytes)
                    temp_files.append(temp_file)
                
                # 合并PDF文件
                for temp_file in temp_files:
                    with open(temp_file, 'rb') as f:
                        merger.append(f)
                
                # 写入合并后的PDF
                with open(output_path, 'wb') as f:
                    merger.write(f)
                
                merger.close()
            
            logger.info(f"PDF合并完成: {output_path} (共 {len(pdf_bytes_list)} 页)")
            return output_path
            
        except Exception as e:
            error_msg = f"合并PDF失败: {str(e)}"
            logger.error(error_msg)
            raise CertificateGenerationError(error_msg)
    
    def generate_and_merge_certificates(self, pigeon_data_list: List[PigeonData],
                                       output_path: Union[str, Path],
                                       template_name: str = 'certificate_template.html',
                                       additional_data: Optional[Dict[str, Any]] = None,
                                       progress_callback: Optional[Callable[[int, int], None]] = None) -> Path:
        """
        生成并合并证书PDF
        Generate and merge certificate PDFs
        
        Args:
            pigeon_data_list: 信鸽数据列表
            output_path: 输出文件路径
            template_name: 模板文件名
            additional_data: 额外数据
            progress_callback: 进度回调函数
            
        Returns:
            Path: 合并后的PDF文件路径
        """
        try:
            # 批量生成证书
            pdf_list = self.generate_batch_certificates(
                pigeon_data_list, template_name, additional_data, progress_callback
            )
            
            # 合并PDF
            merged_path = self.merge_certificates(pdf_list, output_path)
            
            return merged_path
            
        except Exception as e:
            error_msg = f"生成并合并证书失败: {str(e)}"
            logger.error(error_msg)
            raise CertificateGenerationError(error_msg)
    
    def validate_pdf_output(self, pdf_bytes: bytes) -> bool:
        """
        验证PDF输出
        Validate PDF output
        
        Args:
            pdf_bytes: PDF字节数据
            
        Returns:
            bool: 是否有效
        """
        try:
            # 检查PDF头部
            if not pdf_bytes.startswith(b'%PDF-'):
                return False
            
            # 尝试读取PDF
            with tempfile.NamedTemporaryFile() as temp_file:
                temp_file.write(pdf_bytes)
                temp_file.flush()
                
                reader = PdfReader(temp_file.name)
                page_count = len(reader.pages)
                
                if page_count == 0:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"PDF验证失败: {e}")
            return False
    
    def get_pdf_info(self, pdf_bytes: bytes) -> Dict[str, Any]:
        """
        获取PDF信息
        Get PDF information
        
        Args:
            pdf_bytes: PDF字节数据
            
        Returns:
            dict: PDF信息
        """
        try:
            with tempfile.NamedTemporaryFile() as temp_file:
                temp_file.write(pdf_bytes)
                temp_file.flush()
                
                reader = PdfReader(temp_file.name)
                
                info = {
                    'page_count': len(reader.pages),
                    'size_bytes': len(pdf_bytes),
                    'is_valid': True
                }
                
                # 获取元数据
                if reader.metadata:
                    info['metadata'] = {
                        'title': reader.metadata.get('/Title', ''),
                        'author': reader.metadata.get('/Author', ''),
                        'creator': reader.metadata.get('/Creator', ''),
                        'producer': reader.metadata.get('/Producer', '')
                    }
                
                return info
                
        except Exception as e:
            logger.error(f"获取PDF信息失败: {e}")
            return {
                'page_count': 0,
                'size_bytes': len(pdf_bytes),
                'is_valid': False,
                'error': str(e)
            }


def demo_certificate_generator():
    """证书生成器演示"""
    print("=== 证书生成器演示 ===\n")
    
    try:
        # 检查依赖
        if not WEASYPRINT_AVAILABLE:
            print("❌ WeasyPrint 未安装")
            return
        
        if not PYPDF2_AVAILABLE:
            print("❌ PyPDF2 未安装")
            return
        
        # 创建证书生成器
        generator = CertificateGenerator()
        print("1. 证书生成器创建成功")
        
        # 创建测试数据
        test_data = PigeonData(
            item_no="001",
            pigeon_id="CN-2025-001",
            gender="雄",
            mstn="CC",
            f_ker="TT", 
            ldha="AA",
            drd4="GG",
            cry1="CC"
        )
        
        print("2. 测试数据创建完成")
        
        # 生成单页证书
        print("\n3. 生成单页证书:")
        try:
            additional_data = {
                'report_number': 'TEST-2025-001',
                'submitter': '测试用户',
                'sample_date': '2025-01-01',
                'receive_date': '2025-01-02',
                'print_date': '2025-01-03'
            }
            
            pdf_bytes = generator.generate_single_certificate(test_data, additional_data=additional_data)
            print(f"   单页证书生成: ✓ 成功 (大小: {len(pdf_bytes)} 字节)")
            
            # 验证PDF
            is_valid = generator.validate_pdf_output(pdf_bytes)
            print(f"   PDF验证: {'✓' if is_valid else '✗'} {'有效' if is_valid else '无效'}")
            
            # 获取PDF信息
            pdf_info = generator.get_pdf_info(pdf_bytes)
            print(f"   PDF页数: {pdf_info.get('page_count', 0)}")
            print(f"   PDF大小: {pdf_info.get('size_bytes', 0)} 字节")
            
        except Exception as e:
            print(f"   单页证书生成: ✗ 失败 - {e}")
        
        # 测试批量生成
        print("\n4. 测试批量生成:")
        try:
            test_data_list = []
            for i in range(3):
                data = PigeonData(
                    item_no=f"{i+1:03d}",
                    pigeon_id=f"CN-2025-{i+1:03d}",
                    gender="雄" if i % 2 == 0 else "雌",
                    mstn=["CC", "CT", "TT"][i % 3],
                    f_ker=["TT", "TC", "CC"][i % 3],
                    ldha=["AA", "AB", "BB"][i % 3],
                    drd4=["GG", "GA", "AA"][i % 3],
                    cry1=["CC", "CT", "TT"][i % 3]
                )
                test_data_list.append(data)
            
            def progress_callback(current, total):
                print(f"   进度: {current}/{total}")
            
            pdf_list = generator.generate_batch_certificates(
                test_data_list, 
                additional_data=additional_data,
                progress_callback=progress_callback
            )
            
            print(f"   批量生成: ✓ 成功 (生成 {len(pdf_list)} 个PDF)")
            
            # 测试合并
            output_path = Path("output/merged_certificates.pdf")
            merged_path = generator.merge_certificates(pdf_list, output_path)
            print(f"   PDF合并: ✓ 成功 - {merged_path}")
            
        except Exception as e:
            print(f"   批量生成: ✗ 失败 - {e}")
        
        print("\n=== 演示完成 ===")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行演示
    demo_certificate_generator()