#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聚合报告生成器模块 - 生成A4横版汇总报告
Report Generator Module - Generate A4 landscape aggregate reports
"""

import os
import logging
from typing import List, Dict, Any, Optional, Union, Callable, Tuple
from pathlib import Path
import tempfile
import math

try:
    import weasyprint
    from weasyprint import HTML, CSS
    WEASYPRINT_AVAILABLE = True
except (ImportError, OSError):
    WEASYPRINT_AVAILABLE = False
    HTML = None
    CSS = None

try:
    from PyPDF2 import PdfMerger, PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    PdfMerger = None
    PdfReader = None

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4, landscape
    from reportlab.lib.units import mm
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from .template_manager import TemplateManager
from .image_processor import ImageProcessor
from .data_models import PigeonData
from .font_manager import font_manager

logger = logging.getLogger(__name__)


class ReportGenerationError(Exception):
    """报告生成错误异常"""
    pass


class ReportGenerator:
    """
    聚合报告生成器
    Aggregate Report Generator
    
    负责生成A4横版汇总报告，支持分页显示多个信鸽数据
    Responsible for generating A4 landscape aggregate reports with pagination for multiple pigeon data
    """
    
    # 分页配置
    ROWS_PER_PAGE = 20  # 每页显示的数据行数
    
    def __init__(self, template_manager: Optional[TemplateManager] = None,
                 image_processor: Optional[ImageProcessor] = None):
        """
        初始化报告生成器
        Initialize report generator

        Args:
            template_manager: 模板管理器实例
            image_processor: 图像处理器实例
        """
        # 不在初始化时检查PDF库，而是在实际使用时检查
        # 这样可以让程序正常启动，只在需要生成PDF时才报错
        self.pdf_libraries_available = WEASYPRINT_AVAILABLE or REPORTLAB_AVAILABLE
        self.pypdf2_available = PYPDF2_AVAILABLE

        if not self.pdf_libraries_available:
            logger.warning("PDF生成库未安装，PDF生成功能将不可用")

        if not self.pypdf2_available:
            logger.warning("PyPDF2未安装，PDF合并功能将不可用")
        
        # 初始化依赖组件
        self.template_manager = template_manager or TemplateManager()
        self.image_processor = image_processor or ImageProcessor()
        
        # PDF生成配置
        self.pdf_config = {
            'page_size': 'A4',
            'orientation': 'landscape',  # 横版
            'margin': '15mm',
            'encoding': 'utf-8'
        }
        
        # 中文字体配置
        self.font_config = self._setup_font_config()
        
        # 优先使用 WeasyPrint (支持HTML模板)，ReportLab作为备用
        self.use_weasyprint = WEASYPRINT_AVAILABLE
        
        logger.info("聚合报告生成器初始化完成")
    
    def _setup_font_config(self) -> Dict[str, Any]:
        """
        设置中文字体配置
        Setup Chinese font configuration
        
        Returns:
            dict: 字体配置字典
        """
        font_config = {
            'font_family': font_manager.get_font_family_css(),
            'font_size': '10pt',
            'line_height': '1.2',
            'header_font_size': '14pt',
            'title_font_size': '16pt',
            'chinese_font': font_manager.get_chinese_font(bold=False),
            'chinese_bold_font': font_manager.get_chinese_font(bold=True)
        }

        logger.info(f"报告字体配置: {font_config}")
        return font_config
    
    def _create_css_styles(self) -> str:
        """
        创建横版PDF样式CSS
        Create landscape PDF CSS styles
        
        Returns:
            str: CSS样式字符串
        """
        css_styles = f"""
        @page {{
            size: {self.pdf_config['page_size']} {self.pdf_config['orientation']};
            margin: {self.pdf_config['margin']};
        }}
        
        body {{
            font-family: {self.font_config['font_family']};
            font-size: {self.font_config['font_size']};
            line-height: {self.font_config['line_height']};
            margin: 0;
            padding: 0;
        }}
        
        .report {{
            width: 100%;
            height: 100%;
        }}
        
        .report-header {{
            text-align: center;
            margin-bottom: 15px;
        }}
        
        .report-title {{
            font-size: {self.font_config['title_font_size']};
            font-weight: bold;
            margin-bottom: 10px;
        }}
        
        .report-info {{
            font-size: {self.font_config['font_size']};
            margin-bottom: 15px;
        }}
        
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 9pt;
        }}
        
        .data-table th,
        .data-table td {{
            border: 1px solid #000;
            padding: 4px 2px;
            text-align: center;
            vertical-align: middle;
        }}
        
        .data-table th {{
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 9pt;
        }}
        
        .data-table .col-no {{ width: 5%; }}
        .data-table .col-id {{ width: 12%; }}
        .data-table .col-gender {{ width: 6%; }}
        .data-table .col-gene {{ width: 8%; }}
        .data-table .col-eval {{ width: 8%; }}
        
        .page-footer {{
            position: fixed;
            bottom: 10mm;
            right: 15mm;
            font-size: 8pt;
            color: #666;
        }}
        
        .signature-section {{
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .seal-image,
        .signature-image {{
            max-width: 80px;
            max-height: 80px;
        }}
        """
        
        return css_styles
    
    def paginate_data(self, pigeon_data_list: List[PigeonData], 
                     rows_per_page: Optional[int] = None) -> List[List[PigeonData]]:
        """
        将数据按页分组
        Paginate data into pages
        
        Args:
            pigeon_data_list: 信鸽数据列表
            rows_per_page: 每页行数，如果为None则使用默认值
            
        Returns:
            List[List[PigeonData]]: 分页后的数据列表
        """
        if rows_per_page is None:
            rows_per_page = self.ROWS_PER_PAGE
        
        if not pigeon_data_list:
            return []
        
        pages = []
        total_items = len(pigeon_data_list)
        total_pages = math.ceil(total_items / rows_per_page)
        
        for page_num in range(total_pages):
            start_idx = page_num * rows_per_page
            end_idx = min(start_idx + rows_per_page, total_items)
            page_data = pigeon_data_list[start_idx:end_idx]
            pages.append(page_data)
        
        logger.info(f"数据分页完成: {total_items} 条记录分为 {total_pages} 页")
        return pages
    
    def _evaluate_genotypes(self, pigeon_data: PigeonData) -> Dict[str, str]:
        """
        评价基因型
        Evaluate genotypes
        
        Args:
            pigeon_data: 信鸽数据
            
        Returns:
            dict: 基因型评价结果
        """
        evaluations = {}
        
        # 基因型评价规则
        evaluation_rules = {
            'mstn': {
                'CC': '优',
                'CT': '佳', 
                'TT': '普通'
            },
            'f_ker': {
                'TT': '优',
                'TC': '佳',
                'CC': '普通'
            },
            'ldha': {
                'AA': '优',
                'AB': '佳',
                'BB': '普通'
            },
            'drd4a': {
                'GG': '优',
                'GA': '佳',
                'AA': '普通'
            },
            'drd4b': {
                'GG': '优',
                'GA': '佳',
                'AA': '普通'
            },
            'cry1': {
                'CC': '优',
                'CT': '佳',
                'TT': '普通'
            }
        }
        
        # 应用评价规则
        for gene, rules in evaluation_rules.items():
            gene_value = getattr(pigeon_data, gene, None)
            if gene_value and gene_value in rules:
                evaluations[f'{gene}_evaluation'] = rules[gene_value]
            else:
                evaluations[f'{gene}_evaluation'] = '未知'
        
        return evaluations
    
    def _prepare_report_data(self, pigeon_data_list: List[PigeonData],
                           additional_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        准备报告数据
        Prepare report data
        
        Args:
            pigeon_data_list: 信鸽数据列表
            additional_data: 额外数据
            
        Returns:
            dict: 准备好的报告数据
        """
        # 确保additional_data不为None
        if additional_data is None:
            additional_data = {}

        # 基础报告信息
        report_data = {
            'total_count': len(pigeon_data_list),
            'report_date': additional_data.get('report_date', ''),
            'submitter': additional_data.get('submitter', ''),
            'report_number': additional_data.get('report_number', ''),
            'submit_date': additional_data.get('submit_date', ''),
        }

        # 添加额外数据
        if additional_data:
            report_data.update(additional_data)
        
        # 处理每个信鸽数据，添加基因型评价
        processed_data = []
        for pigeon_data in pigeon_data_list:
            pigeon_dict = pigeon_data.to_dict()
            evaluations = self._evaluate_genotypes(pigeon_data)
            pigeon_dict.update(evaluations)
            processed_data.append(pigeon_dict)
        
        report_data['pigeon_data'] = processed_data
        
        # 分页数据
        pages = self.paginate_data(pigeon_data_list)
        processed_pages = []
        for page_data in pages:
            page_processed = []
            for pigeon_data in page_data:
                pigeon_dict = pigeon_data.to_dict()
                evaluations = self._evaluate_genotypes(pigeon_data)
                pigeon_dict.update(evaluations)
                page_processed.append(pigeon_dict)
            processed_pages.append(page_processed)
        
        report_data['pages'] = processed_pages
        
        # 添加图片数据
        try:
            image_context = self.image_processor.create_placeholder_context()
            report_data.update(image_context)
        except Exception as e:
            logger.warning(f"获取图片数据失败: {e}")
            report_data['seal_image'] = None
            report_data['signature_image'] = None
        
        return report_data
    
    def _generate_pdf_with_reportlab_from_template(self, report_data: Dict[str, Any], template_name: str) -> bytes:
        """
        使用ReportLab生成PDF，但基于HTML模板的数据结构（单页A4纵版布局）
        Generate PDF using ReportLab, but based on HTML template data structure (single page A4 portrait)

        Args:
            report_data: 报告数据
            template_name: 模板名称（用于确定样式）

        Returns:
            bytes: PDF字节数据
        """
        from io import BytesIO

        # 创建字节流
        buffer = BytesIO()

        # 创建纵版PDF文档，增加边距为边框留空间
        doc = SimpleDocTemplate(buffer, pagesize=A4,
                               rightMargin=30*mm, leftMargin=30*mm,
                               topMargin=25*mm, bottomMargin=25*mm)

        # 获取样式
        styles = getSampleStyleSheet()

        # 创建自定义样式（紧凑单页布局）
        main_title_style = ParagraphStyle(
            'MainTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=3,
            alignment=1,  # 居中
            fontName=self.font_config['chinese_bold_font'],
            textColor=colors.HexColor('#1a3c6c'),
            letterSpacing=2
        )

        sub_title_style = ParagraphStyle(
            'SubTitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=8,
            alignment=1,  # 居中
            fontName=self.font_config['chinese_bold_font'],
            textColor=colors.HexColor('#c00')
        )

        info_style = ParagraphStyle(
            'InfoStyle',
            parent=styles['Normal'],
            fontSize=12,
            fontName=self.font_config['chinese_bold_font'],
            backColor=colors.HexColor('#e3f2fd'),
            borderColor=colors.HexColor('#bbdefb'),
            borderWidth=1,
            borderRadius=4,
            leftIndent=15,
            rightIndent=15,
            spaceBefore=5,
            spaceAfter=8
        )

        result_title_style = ParagraphStyle(
            'ResultTitle',
            parent=styles['Heading3'],
            fontSize=14,
            spaceAfter=5,
            spaceBefore=8,
            fontName=self.font_config['chinese_bold_font'],
            textColor=colors.HexColor('#1a3c6c'),
            leftIndent=5,
            borderLeft=3,
            borderLeftColor=colors.HexColor('#c00'),
            borderLeftWidth=3
        )

        # 构建PDF内容
        story = []

        # 获取所有数据（不分页，显示在一页内）
        all_data = []
        pages = report_data.get('pages', [])
        for page_data in pages:
            all_data.extend(page_data)

        # 简化的logo区域
        logo_style = ParagraphStyle(
            'Logo',
            parent=styles['Normal'],
            fontSize=11,
            alignment=2,  # 右对齐
            fontName=self.font_config['chinese_bold_font'],
            textColor=colors.white,
            backColor=colors.HexColor('#1a3c6c'),
            borderRadius=20,
            leftIndent=8,
            rightIndent=8,
            spaceBefore=0,
            spaceAfter=5
        )

        story.append(Paragraph("基因检测实验室", logo_style))
        story.append(Spacer(1, 5))

        # 标题部分（紧凑布局）
        story.append(Paragraph("PIGEON FLIGHT ABILITY IDENTIFICATION SANGER SEQUENCING TEST REPORT", main_title_style))
        story.append(Paragraph("赛鸽育种能力鉴定 SANGER 测序法检测鉴定报告", sub_title_style))

        # 简化分隔线
        line_style = ParagraphStyle(
            'Line',
            parent=styles['Normal'],
            fontSize=1,
            spaceAfter=8,
            borderWidth=1,
            borderColor=colors.HexColor('#1a3c6c')
        )
        story.append(Paragraph("", line_style))

        # 报告信息（紧凑格式）
        info_text = f"送检单位/人：{report_data.get('submitter', '')}　　送检日期：{report_data.get('submit_date', '')}　　报告编号：{report_data.get('report_number', '')}"
        story.append(Paragraph(info_text, info_style))

        # 检测结果标题
        story.append(Paragraph("检测结果", result_title_style))

        # 数据表格（紧凑单页布局）
        table_data = [
            ['序号', '信鸽足环号', '性别', 'LDHA', 'DRD4a', 'DRD4b', 'CRY1', 'MSTN', 'F-KER']
        ]

        for row in all_data:
            table_data.append([
                str(row.get('item_no', '')),
                str(row.get('pigeon_id', '')),
                str(row.get('gender', '')),
                str(row.get('ldha', '')),
                str(row.get('drd4a', '')),
                str(row.get('drd4b', '')),
                str(row.get('cry1', '')),
                str(row.get('mstn', '')),
                str(row.get('f_ker', ''))
            ])

        # 计算列宽（纵版A4，减去边距）
        available_width = A4[0] - 60*mm  # 减去左右边距
        col_widths = [
            available_width * 0.08,  # 序号
            available_width * 0.18,  # 足环号
            available_width * 0.08,  # 性别
            available_width * 0.11,  # LDHA
            available_width * 0.11,  # DRD4a
            available_width * 0.11,  # DRD4b
            available_width * 0.11,  # CRY1
            available_width * 0.11,  # MSTN
            available_width * 0.11   # F-KER
        ]

        data_table = Table(table_data, colWidths=col_widths)
        data_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), self.font_config['chinese_font']),
            ('FONTSIZE', (0, 0), (-1, -1), 10),  # 减小字体
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#ddd')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 0), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
            # 表头样式
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1a3c6c')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), self.font_config['chinese_bold_font']),
            ('FONTSIZE', (0, 0), (-1, 0), 11),  # 表头字体稍大
            # 行高控制
            ('ROWHEIGHT', (0, 0), (-1, -1), 18),  # 紧凑行高
        ]))

        story.append(data_table)

        # 添加简化的注释和页脚
        story.append(Spacer(1, 10))

        note_style = ParagraphStyle(
            'Note',
            parent=styles['Normal'],
            fontSize=10,
            alignment=1,  # 居中
            fontName=self.font_config['chinese_font'],
            textColor=colors.HexColor('#777'),
            spaceAfter=5
        )

        story.append(Paragraph("**注：本报告的检测结果仅对本次送检的样本负责。**", note_style))

        # 简化页脚
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=10,
            alignment=1,  # 居中
            fontName=self.font_config['chinese_font'],
            textColor=colors.HexColor('#555'),
            spaceBefore=5
        )

        story.append(Paragraph("赛鸽基因检测中心 | 专业赛鸽育种能力鉴定机构", footer_style))

        # 构建PDF
        doc.build(story)

        # 获取PDF字节数据
        pdf_bytes = buffer.getvalue()
        buffer.close()

        return pdf_bytes

    def _generate_pdf_with_reportlab(self, report_data: Dict[str, Any]) -> bytes:
        """
        使用ReportLab生成横版PDF报告
        Generate landscape PDF report using ReportLab
        
        Args:
            report_data: 报告数据
            
        Returns:
            bytes: PDF字节数据
        """
        from io import BytesIO
        
        # 创建字节流
        buffer = BytesIO()
        
        # 创建横版PDF文档
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4), 
                               rightMargin=15*mm, leftMargin=15*mm,
                               topMargin=15*mm, bottomMargin=15*mm)
        
        # 获取样式
        styles = getSampleStyleSheet()
        
        # 创建自定义样式（使用中文字体）
        title_style = ParagraphStyle(
            'ReportTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=20,
            alignment=1,  # 居中
            fontName=self.font_config['chinese_bold_font']
        )

        info_style = ParagraphStyle(
            'ReportInfo',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=10,
            fontName=self.font_config['chinese_font']
        )
        
        # 构建内容
        story = []
        
        # 报告标题
        title = Paragraph("信鸽基因检测汇总报告", title_style)
        story.append(title)
        
        # 报告基本信息
        info_data = [
            ['报告编号:', report_data.get('report_number', ''), '送检人:', report_data.get('submitter', '')],
            ['送检日期:', report_data.get('submit_date', ''), '报告日期:', report_data.get('report_date', '')],
            ['检测总数:', str(report_data.get('total_count', 0)), '', '']
        ]
        
        info_table = Table(info_data, colWidths=[25*mm, 40*mm, 25*mm, 40*mm])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 15))
        
        # 处理分页数据
        pages = report_data.get('pages', [])
        
        for page_num, page_data in enumerate(pages):
            if page_num > 0:
                story.append(PageBreak())
            
            # 数据表格标题
            table_header = [
                '序号', '足环号', '性别', 'MSTN', '评价', 'F-KER', '评价', 
                'LDHA', '评价', 'DRD4a', '评价', 'DRD4b', '评价', 'CRY1', '评价'
            ]
            
            # 数据行
            table_data = [table_header]
            
            for i, pigeon in enumerate(page_data):
                row = [
                    pigeon.get('item_no', ''),
                    pigeon.get('pigeon_id', ''),
                    pigeon.get('gender', ''),
                    pigeon.get('mstn', ''),
                    pigeon.get('mstn_evaluation', ''),
                    pigeon.get('f_ker', ''),
                    pigeon.get('f_ker_evaluation', ''),
                    pigeon.get('ldha', ''),
                    pigeon.get('ldha_evaluation', ''),
                    pigeon.get('drd4a', ''),
                    pigeon.get('drd4a_evaluation', ''),
                    pigeon.get('drd4b', ''),
                    pigeon.get('drd4b_evaluation', ''),
                    pigeon.get('cry1', ''),
                    pigeon.get('cry1_evaluation', '')
                ]
                table_data.append(row)
            
            # 创建表格
            col_widths = [12*mm, 25*mm, 12*mm, 15*mm, 12*mm, 15*mm, 12*mm, 
                         15*mm, 12*mm, 15*mm, 12*mm, 15*mm, 12*mm, 15*mm, 12*mm]
            
            data_table = Table(table_data, colWidths=col_widths)
            data_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), self.font_config['chinese_font']),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('FONTNAME', (0, 0), (-1, 0), self.font_config['chinese_bold_font']),
                ('FONTSIZE', (0, 0), (-1, 0), 9),
            ]))
            
            story.append(data_table)
            
            # 页脚信息
            if page_num == len(pages) - 1:  # 最后一页添加签名
                story.append(Spacer(1, 20))
                signature_text = Paragraph("检测机构签章：________________    日期：________________", info_style)
                story.append(signature_text)
        
        # 构建PDF
        doc.build(story)
        
        # 获取PDF字节数据
        pdf_bytes = buffer.getvalue()
        buffer.close()
        
        return pdf_bytes
    
    def _generate_pdf_with_weasyprint(self, report_data: Dict[str, Any], template_name: str) -> bytes:
        """
        使用WeasyPrint生成PDF报告
        Generate PDF report using WeasyPrint
        
        Args:
            report_data: 报告数据
            template_name: 模板名称
            
        Returns:
            bytes: PDF字节数据
        """
        # 渲染HTML模板
        html_content = self.template_manager.render_template(template_name, report_data)
        
        # 创建CSS样式
        css_content = self._create_css_styles()
        
        # 生成PDF
        html_doc = HTML(string=html_content, encoding=self.pdf_config['encoding'])
        css_doc = CSS(string=css_content)
        
        # 生成PDF字节数据
        pdf_bytes = html_doc.write_pdf(stylesheets=[css_doc])
        
        return pdf_bytes
    
    def generate_aggregate_report(self, pigeon_data_list: List[PigeonData],
                                 template_name: str = 'report_template.html',
                                 additional_data: Optional[Dict[str, Any]] = None,
                                 output_path: Optional[Union[str, Path]] = None,
                                 progress_callback: Optional[Callable[[int, int], None]] = None) -> bytes:
        """
        生成聚合报告PDF
        Generate aggregate report PDF

        Args:
            pigeon_data_list: 信鸽数据列表
            template_name: 模板文件名
            additional_data: 额外数据
            output_path: 输出路径，如果为None则返回PDF字节数据
            progress_callback: 进度回调函数

        Returns:
            bytes: PDF字节数据

        Raises:
            ReportGenerationError: 报告生成失败
        """
        # 检查PDF生成库是否可用
        if not self.pdf_libraries_available:
            raise ReportGenerationError("PDF生成库未安装，请运行: pip install weasyprint 或 pip install reportlab")

        try:
            if not pigeon_data_list:
                raise ReportGenerationError("没有数据可生成报告")
            
            # 准备报告数据
            if progress_callback:
                progress_callback(1, 4)
            
            report_data = self._prepare_report_data(pigeon_data_list, additional_data)
            
            if progress_callback:
                progress_callback(2, 4)
            
            # 根据可用库选择生成方法（优先使用WeasyPrint以支持HTML模板）
            if self.use_weasyprint:
                pdf_bytes = self._generate_pdf_with_weasyprint(report_data, template_name)
            elif REPORTLAB_AVAILABLE:
                # 使用基于模板数据结构的ReportLab方法
                pdf_bytes = self._generate_pdf_with_reportlab_from_template(report_data, template_name)
            else:
                raise ReportGenerationError("没有可用的PDF生成库")
            
            if progress_callback:
                progress_callback(3, 4)
            
            # 如果指定了输出路径，保存文件
            if output_path:
                output_path = Path(output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(output_path, 'wb') as f:
                    f.write(pdf_bytes)
                
                logger.info(f"聚合报告已保存: {output_path}")
            
            if progress_callback:
                progress_callback(4, 4)
            
            logger.info(f"聚合报告生成成功: {len(pigeon_data_list)} 条记录")
            return pdf_bytes
            
        except Exception as e:
            error_msg = f"生成聚合报告失败: {str(e)}"
            logger.error(error_msg)
            raise ReportGenerationError(error_msg)
    
    def get_report_statistics(self, pigeon_data_list: List[PigeonData]) -> Dict[str, Any]:
        """
        获取报告统计信息
        Get report statistics
        
        Args:
            pigeon_data_list: 信鸽数据列表
            
        Returns:
            dict: 统计信息
        """
        if not pigeon_data_list:
            return {
                'total_count': 0,
                'gender_stats': {},
                'gene_stats': {},
                'evaluation_stats': {}
            }
        
        stats = {
            'total_count': len(pigeon_data_list),
            'gender_stats': {},
            'gene_stats': {},
            'evaluation_stats': {}
        }
        
        # 性别统计
        gender_count = {}
        for pigeon in pigeon_data_list:
            gender = pigeon.gender or '未知'
            gender_count[gender] = gender_count.get(gender, 0) + 1
        stats['gender_stats'] = gender_count
        
        # 基因型统计
        genes = ['mstn', 'f_ker', 'ldha', 'drd4a', 'drd4b', 'cry1']
        gene_stats = {}
        
        for gene in genes:
            gene_count = {}
            for pigeon in pigeon_data_list:
                gene_value = getattr(pigeon, gene, None) or '未知'
                gene_count[gene_value] = gene_count.get(gene_value, 0) + 1
            gene_stats[gene] = gene_count
        
        stats['gene_stats'] = gene_stats
        
        # 评价统计
        evaluation_stats = {'优': 0, '佳': 0, '普通': 0, '未知': 0}
        
        for pigeon in pigeon_data_list:
            evaluations = self._evaluate_genotypes(pigeon)
            for eval_key, eval_value in evaluations.items():
                if eval_value in evaluation_stats:
                    evaluation_stats[eval_value] += 1
        
        stats['evaluation_stats'] = evaluation_stats
        
        return stats
    
    def validate_report_data(self, pigeon_data_list: List[PigeonData]) -> Tuple[bool, List[str]]:
        """
        验证报告数据
        Validate report data
        
        Args:
            pigeon_data_list: 信鸽数据列表
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        if not pigeon_data_list:
            return False, ["没有数据可生成报告"]
        
        errors = []
        
        # 检查数据完整性
        for i, pigeon in enumerate(pigeon_data_list):
            if not pigeon.is_valid():
                errors.append(f"第 {i+1} 行数据无效: {pigeon}")
        
        # 检查是否有重复的足环号
        pigeon_ids = [p.pigeon_id for p in pigeon_data_list if p.pigeon_id]
        duplicate_ids = []
        seen_ids = set()
        
        for pigeon_id in pigeon_ids:
            if pigeon_id in seen_ids:
                if pigeon_id not in duplicate_ids:
                    duplicate_ids.append(pigeon_id)
            else:
                seen_ids.add(pigeon_id)
        
        if duplicate_ids:
            errors.append(f"发现重复的足环号: {', '.join(duplicate_ids)}")
        
        return len(errors) == 0, errors


def demo_report_generator():
    """聚合报告生成器演示"""
    print("=== 聚合报告生成器演示 ===\n")
    
    try:
        # 创建报告生成器
        generator = ReportGenerator()
        print("1. 报告生成器创建成功")
        print(f"   使用ReportLab: {generator.use_reportlab}")
        
        # 创建测试数据
        test_data_list = []
        genotypes = [
            ("CC", "TT", "AA", "GG", "GG", "CC"),
            ("CT", "TC", "AB", "GA", "GA", "CT"),
            ("TT", "CC", "BB", "AA", "AA", "TT"),
            ("CC", "TT", "AB", "GG", "GA", "CC"),
            ("CT", "TC", "AA", "GA", "GG", "CT")
        ]
        
        for i in range(25):  # 创建25条数据测试分页
            mstn, f_ker, ldha, drd4a, drd4b, cry1 = genotypes[i % len(genotypes)]
            data = PigeonData(
                item_no=f"{i+1:03d}",
                pigeon_id=f"CN-2025-{i+1:03d}",
                gender="雄" if i % 2 == 0 else "雌",
                mstn=mstn,
                f_ker=f_ker,
                ldha=ldha,
                drd4a=drd4a,
                drd4b=drd4b,
                cry1=cry1
            )
            test_data_list.append(data)
        
        print(f"2. 测试数据创建完成: {len(test_data_list)} 条记录")
        
        # 测试数据分页
        print("\n3. 测试数据分页:")
        pages = generator.paginate_data(test_data_list)
        print(f"   分页结果: {len(pages)} 页")
        for i, page in enumerate(pages):
            print(f"   第 {i+1} 页: {len(page)} 条记录")
        
        # 测试数据验证
        print("\n4. 测试数据验证:")
        is_valid, errors = generator.validate_report_data(test_data_list)
        print(f"   数据验证: {'✓ 通过' if is_valid else '✗ 失败'}")
        if errors:
            for error in errors:
                print(f"   错误: {error}")
        
        # 获取统计信息
        print("\n5. 获取统计信息:")
        stats = generator.get_report_statistics(test_data_list)
        print(f"   总数: {stats['total_count']}")
        print(f"   性别分布: {stats['gender_stats']}")
        print(f"   评价分布: {stats['evaluation_stats']}")
        
        # 生成聚合报告
        print("\n6. 生成聚合报告:")
        try:
            additional_data = {
                'report_number': 'REPORT-2025-001',
                'submitter': '测试单位',
                'submit_date': '2025-01-01',
                'report_date': '2025-01-03'
            }
            
            # 创建输出目录
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            def progress_callback(current, total):
                print(f"   进度: {current}/{total}")
            
            # 生成报告
            output_path = output_dir / "test_aggregate_report.pdf"
            pdf_bytes = generator.generate_aggregate_report(
                test_data_list,
                additional_data=additional_data,
                output_path=output_path,
                progress_callback=progress_callback
            )
            
            print(f"   聚合报告生成: ✓ 成功 (大小: {len(pdf_bytes)} 字节)")
            print(f"   保存路径: {output_path}")
            
        except Exception as e:
            print(f"   聚合报告生成: ✗ 失败 - {e}")
        
        print("\n=== 演示完成 ===")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行演示
    demo_report_generator()