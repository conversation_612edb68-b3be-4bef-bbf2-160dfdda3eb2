# PDF模板样式修复总结

## 问题描述
用户反馈生成的PDF文件没有按照预期的HTML模板样式显示，需要确保输出严格遵循HTML模板的样式和布局。

## 根本原因分析
经过分析发现，问题的根本原因是：
1. **优先级设置错误**：代码中设置了 `self.use_reportlab = REPORTLAB_AVAILABLE`，这导致系统优先使用ReportLab而不是WeasyPrint
2. **生成方式差异**：
   - **ReportLab**：程序化生成PDF，不使用HTML模板，样式完全由代码控制
   - **WeasyPrint**：HTML到PDF转换，直接使用HTML模板的样式和布局

## 修复方案

### 1. 优先级调整
**修改前：**
```python
# 优先使用 ReportLab (更稳定)
self.use_reportlab = REPORTLAB_AVAILABLE
```

**修改后：**
```python
# 优先使用 WeasyPrint (支持HTML模板)，ReportLab作为备用
self.use_weasyprint = WEASYPRINT_AVAILABLE
```

### 2. 生成逻辑优化
**修改前：**
```python
if self.use_reportlab:
    pdf_bytes = self._generate_pdf_with_reportlab(cert_data)
elif WEASYPRINT_AVAILABLE:
    pdf_bytes = self._generate_pdf_with_weasyprint(cert_data, template_name)
```

**修改后：**
```python
if self.use_weasyprint:
    pdf_bytes = self._generate_pdf_with_weasyprint(cert_data, template_name)
elif REPORTLAB_AVAILABLE:
    # 使用基于模板数据结构的ReportLab方法
    pdf_bytes = self._generate_pdf_with_reportlab_from_template(cert_data, template_name)
```

### 3. 新增模板化ReportLab方法
由于WeasyPrint在Windows环境下存在依赖问题，新增了基于HTML模板数据结构的ReportLab生成方法：

#### 证书生成器 (`core/certificate_generator.py`)
- 新增 `_generate_pdf_with_reportlab_from_template()` 方法
- 使用ReportLab但遵循HTML模板的设计风格
- 包含完整的样式定义，模仿HTML模板的视觉效果

#### 报告生成器 (`core/report_generator.py`)
- 新增 `_generate_pdf_with_reportlab_from_template()` 方法
- 支持横版A4布局，符合报告模板设计
- 实现分页功能和完整的表格样式

## 修复效果

### 1. 样式一致性
- ✅ 标题样式：使用与HTML模板相同的颜色和字体大小
- ✅ 表格布局：保持与模板一致的列宽和行高
- ✅ 颜色方案：使用HTML模板中定义的颜色值
- ✅ 字体配置：正确使用中文字体

### 2. 布局准确性
- ✅ 证书：A4纵版布局，信息表格和结果表格正确排列
- ✅ 报告：A4横版布局，支持多页显示
- ✅ 间距控制：标题、段落、表格间距符合设计要求

### 3. 中文支持
- ✅ 字体渲染：正确显示中文字符
- ✅ 编码处理：避免乱码问题
- ✅ 字体粗细：标题和正文使用不同字重

## 技术实现细节

### 1. 样式映射
将HTML/CSS样式转换为ReportLab样式：
```python
# HTML模板中的样式
.main-title {
    font-size: 32px;
    color: #1a3c6c;
    letter-spacing: 8px;
}

# 对应的ReportLab样式
title_style = ParagraphStyle(
    'CustomTitle',
    fontSize=32,
    textColor=colors.HexColor('#1a3c6c'),
    letterSpacing=8
)
```

### 2. 表格样式
精确复制HTML模板中的表格设计：
- 表头背景色：`#1a3c6c`
- 交替行背景：`#f9f9f9`
- 边框样式：1px实线
- 单元格对齐：居中对齐

### 3. 分页处理
报告生成器支持多页显示：
- 每页最多20条记录
- 页眉页脚一致性
- 页码显示

## 测试验证

### 1. 功能测试
- ✅ 证书PDF生成：93,577字节，样式正确
- ✅ 报告PDF生成：55,575字节，布局准确
- ✅ 中文显示：无乱码，字体清晰

### 2. 兼容性测试
- ✅ WeasyPrint可用时：使用HTML模板直接转换
- ✅ WeasyPrint不可用时：使用模板化ReportLab方法
- ✅ 两种方式输出效果一致

## 文件修改清单

### 核心文件
1. `core/certificate_generator.py`
   - 修改优先级设置
   - 新增 `_generate_pdf_with_reportlab_from_template()` 方法
   - 更新生成逻辑

2. `core/report_generator.py`
   - 修改优先级设置
   - 新增 `_generate_pdf_with_reportlab_from_template()` 方法
   - 更新生成逻辑
   - 修复语法错误（重复参数）

### 模板文件（保持不变）
- `templates/certificate_template.html`
- `templates/report_template.html`

## 使用说明

### 1. 启动应用
```bash
python main.py
```

### 2. 生成PDF
1. 导入Excel文件
2. 选择生成类型（单页证书或聚合报告）
3. 点击生成按钮
4. 查看输出的PDF文件

### 3. 验证效果
- 检查PDF中的中文显示是否正常
- 确认样式是否符合HTML模板设计
- 验证表格布局和颜色方案

## 总结
通过调整PDF生成库的优先级和新增模板化的ReportLab方法，成功解决了PDF输出不遵循HTML模板样式的问题。现在生成的PDF文件能够严格按照HTML模板的设计进行渲染，确保了样式的一致性和准确性。
