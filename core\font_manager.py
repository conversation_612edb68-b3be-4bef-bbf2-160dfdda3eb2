#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体管理模块
Font Management Module

处理PDF生成中的中文字体支持
Handles Chinese font support for PDF generation
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)

# 尝试导入ReportLab字体相关模块
try:
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

class FontManager:
    """字体管理器"""
    
    def __init__(self):
        """初始化字体管理器"""
        self.registered_fonts = {}
        self.default_chinese_font = None
        self.default_chinese_bold_font = None
        
        # 初始化字体
        if REPORTLAB_AVAILABLE:
            self._register_chinese_fonts()
        
        logger.info("字体管理器初始化完成")
    
    def _get_system_font_paths(self) -> List[str]:
        """
        获取系统字体路径
        Get system font paths
        
        Returns:
            List[str]: 字体目录路径列表
        """
        font_paths = []
        
        # Windows字体路径
        if os.name == 'nt':
            windows_fonts = [
                r"C:\Windows\Fonts",
                r"C:\WINDOWS\Fonts",
                os.path.expanduser("~\\AppData\\Local\\Microsoft\\Windows\\Fonts")
            ]
            font_paths.extend([p for p in windows_fonts if os.path.exists(p)])
        
        # macOS字体路径
        elif os.name == 'posix' and os.uname().sysname == 'Darwin':
            macos_fonts = [
                "/System/Library/Fonts",
                "/Library/Fonts",
                os.path.expanduser("~/Library/Fonts")
            ]
            font_paths.extend([p for p in macos_fonts if os.path.exists(p)])
        
        # Linux字体路径
        else:
            linux_fonts = [
                "/usr/share/fonts",
                "/usr/local/share/fonts",
                os.path.expanduser("~/.fonts"),
                os.path.expanduser("~/.local/share/fonts")
            ]
            font_paths.extend([p for p in linux_fonts if os.path.exists(p)])
        
        return font_paths
    
    def _find_chinese_fonts(self) -> Dict[str, str]:
        """
        查找系统中的中文字体
        Find Chinese fonts in system
        
        Returns:
            Dict[str, str]: 字体名称到文件路径的映射
        """
        chinese_fonts = {}
        font_paths = self._get_system_font_paths()
        
        # 常见的中文字体文件名
        chinese_font_files = {
            'SimSun': ['simsun.ttc', 'simsun.ttf', 'SimSun.ttf'],
            'SimHei': ['simhei.ttf', 'SimHei.ttf'],
            'Microsoft YaHei': ['msyh.ttc', 'msyh.ttf', 'Microsoft YaHei.ttf'],
            'KaiTi': ['kaiti.ttf', 'KaiTi.ttf'],
            'FangSong': ['simfang.ttf', 'FangSong.ttf'],
            'NotoSansCJK': ['NotoSansCJK-Regular.ttc', 'NotoSansSC-Regular.otf'],
            'SourceHanSans': ['SourceHanSansSC-Regular.otf', 'SourceHanSans-Regular.ttc']
        }
        
        for font_dir in font_paths:
            if not os.path.exists(font_dir):
                continue
                
            try:
                for font_name, file_names in chinese_font_files.items():
                    for file_name in file_names:
                        font_file = os.path.join(font_dir, file_name)
                        if os.path.exists(font_file):
                            chinese_fonts[font_name] = font_file
                            logger.info(f"发现中文字体: {font_name} -> {font_file}")
                            break
            except Exception as e:
                logger.warning(f"扫描字体目录失败 {font_dir}: {e}")
        
        return chinese_fonts
    
    def _register_chinese_fonts(self):
        """
        注册中文字体到ReportLab
        Register Chinese fonts to ReportLab
        """
        if not REPORTLAB_AVAILABLE:
            logger.warning("ReportLab不可用，跳过字体注册")
            return
        
        chinese_fonts = self._find_chinese_fonts()
        
        if not chinese_fonts:
            logger.warning("未找到系统中文字体，将使用默认字体")
            return
        
        # 优先级顺序
        font_priority = ['Microsoft YaHei', 'SimSun', 'SimHei', 'NotoSansCJK', 'SourceHanSans', 'KaiTi', 'FangSong']
        
        for font_name in font_priority:
            if font_name in chinese_fonts:
                try:
                    font_path = chinese_fonts[font_name]
                    
                    # 注册常规字体
                    pdfmetrics.registerFont(TTFont(f'Chinese-{font_name}', font_path))
                    self.registered_fonts[f'Chinese-{font_name}'] = font_path
                    
                    # 设置默认中文字体
                    if self.default_chinese_font is None:
                        self.default_chinese_font = f'Chinese-{font_name}'
                        logger.info(f"设置默认中文字体: {self.default_chinese_font}")
                    
                    # 尝试注册粗体版本（如果存在）
                    bold_font_path = font_path.replace('.ttf', '-Bold.ttf').replace('.ttc', '-Bold.ttc')
                    if os.path.exists(bold_font_path):
                        pdfmetrics.registerFont(TTFont(f'Chinese-{font_name}-Bold', bold_font_path))
                        self.registered_fonts[f'Chinese-{font_name}-Bold'] = bold_font_path
                        if self.default_chinese_bold_font is None:
                            self.default_chinese_bold_font = f'Chinese-{font_name}-Bold'
                    else:
                        # 使用常规字体作为粗体
                        if self.default_chinese_bold_font is None:
                            self.default_chinese_bold_font = f'Chinese-{font_name}'
                    
                    logger.info(f"成功注册中文字体: {font_name}")
                    break
                    
                except Exception as e:
                    logger.warning(f"注册字体失败 {font_name}: {e}")
                    continue
        
        # 如果没有找到任何字体，使用备用方案
        if self.default_chinese_font is None:
            logger.warning("未能注册任何中文字体，将使用Helvetica作为备用")
            self.default_chinese_font = 'Helvetica'
            self.default_chinese_bold_font = 'Helvetica-Bold'
    
    def get_chinese_font(self, bold: bool = False) -> str:
        """
        获取中文字体名称
        Get Chinese font name
        
        Args:
            bold: 是否需要粗体
            
        Returns:
            str: 字体名称
        """
        if bold and self.default_chinese_bold_font:
            return self.default_chinese_bold_font
        elif self.default_chinese_font:
            return self.default_chinese_font
        else:
            return 'Helvetica-Bold' if bold else 'Helvetica'
    
    def get_font_family_css(self) -> str:
        """
        获取CSS字体族设置
        Get CSS font family setting
        
        Returns:
            str: CSS字体族字符串
        """
        if self.default_chinese_font:
            # 提取字体名称（去掉Chinese-前缀）
            font_name = self.default_chinese_font.replace('Chinese-', '')
            return f"'{font_name}', 'Microsoft YaHei', 'SimSun', Arial, sans-serif"
        else:
            return "'Microsoft YaHei', 'SimSun', Arial, sans-serif"
    
    def is_chinese_font_available(self) -> bool:
        """
        检查是否有可用的中文字体
        Check if Chinese fonts are available
        
        Returns:
            bool: 是否有中文字体可用
        """
        return self.default_chinese_font is not None and 'Chinese-' in self.default_chinese_font

# 全局字体管理器实例
font_manager = FontManager()
